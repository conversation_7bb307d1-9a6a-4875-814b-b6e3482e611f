
import com.typesafe.sbt.packager.docker.{Cmd, ExecCmd}

ThisBuild / scalaVersion := "3.7.0"

enablePlugins(SonarPlugin)

parallelExecution := false

lazy val root = (project in file("."))
  .aggregate(`profile-service`)
  .settings(
    name := "profile-service",
    version := "1.0"
  )
  .settings(scapegoatSettings)
  .settings(Seq(
    sonarProperties ++= Map(
      "sonar.host.url"                    -> "https://sonar.persgroep.digital",
      "sonar.projectVersion"              -> version.value,
      "sonar.projectKey"                  -> Seq(organization.value, name.value).mkString("."),
      "sonar.projectName"                 -> "Profile service",
      "sonar.projectBaseDir"              -> baseDirectory.value.getPath,
      "sonar.scala.coverage.reportPaths"  -> (crossTarget.value / "scoverage-report" / "scoverage.xml").getPath,
      "sonar.scala.scapegoat.reportPaths" -> (crossTarget.value / "scapegoat-report" / "scapegoat-scalastyle.xml").getPath,
      "sonar.sourceEncoding"              -> "UTF-8",
      "sonar.qualitygate.wait"            -> "true",
      "sonar.tests"                       -> "",
      "sonar.sources"                     -> ""
    )
  ))
  .settings(Seq(commands ++= List(Commands.prepareCommit)))

lazy val `profile-service` = (project in file("modules/profile-service"))
  .enablePlugins(DockerPlugin, JavaAppPackaging, AshScriptPlugin)
  .dependsOn(`profile-search`, `recruiter-favorites`, `saved-search`)
  .aggregate(`profile-search`, `recruiter-favorites`, `saved-search`)
  .settings(Compile / packageBin / mainClass := Some("nl.dpes.profileservice.Main"))
  .settings(libraryDependencies ++= Seq(
    ProfileSearchDependencies.Chimney.`chimney`,
    ProfileSearchDependencies.Chimney.`chimney-cats`,
    ProfileServiceDependencies.Http4s.`http4s-blaze-server`,
    ProfileServiceDependencies.Http4s.`http4s-blaze-client`,
    ProfileServiceDependencies.Tapir.`tapir-core`,
    ProfileServiceDependencies.Tapir.`tapir-http4s-server`,
    ProfileServiceDependencies.Tapir.`tapir-json-circe`,
    ProfileServiceDependencies.Tapir.`tapir-openapi-docs`,
    ProfileServiceDependencies.Tapir.`tapir-swagger-ui-bundle`,
    ProfileServiceDependencies.Tapir.`tapir-sttp-stub-server`,
    ProfileServiceDependencies.PureConfig.`pureconfig-core`,
    ProfileServiceDependencies.Logback.`logback-classic`,
    ProfileServiceDependencies.Logback.`logback-jackson`,
    ProfileServiceDependencies.Logback.`logback-json-classic`,
    ProfileServiceDependencies.Elastic4s.`elastic4s-client-esjava`,
    ProfileServiceDependencies.Elastic4s.`elastic4s-json-circe`,
    ProfileServiceDependencies.Elastic4s.`elastic4s-effect-cats`,
    ProfileServiceDependencies.Doobie.`doobie-core`,
    ProfileServiceDependencies.Log4Cats.`log4cats-slf4j`,
    ProfileServiceDependencies.Log4Cats.`log4cats-testing`,
    ProfileServiceDependencies.Weaver.`weaver-cats`,
    ProfileServiceDependencies.Weaver.`weaver-scalacheck`
  ))
  .settings(
    resolvers ++= Seq(
      DefaultMavenRepository,
      Resolver.typesafeRepo("releases"),
      Resolver.sbtPluginRepo("releases"),
      "snapshots" at "https://archiva.persgroep.digital/repository/snapshots",
      "internal" at "https://archiva.persgroep.digital/repository/internal",
      "releases" at "https://archiva.persgroep.digital/repository/releases"
    ) ++ Resolver.sonatypeOssRepos("public")
  )
  .settings(dockerSettings)
  .settings(scapegoatSettings)
  .settings(
    Seq(
      sonarProperties ++= Map(
        "sonar.host.url"                    -> "https://sonar.persgroep.digital",
        "sonar.projectVersion"              -> version.value,
        "sonar.projectKey"                  -> "profile-service:profile-service",
        "sonar.projectName"                 -> "profile-service",
        "sonar.projectBaseDir"              -> baseDirectory.value.getPath,
        "sonar.scala.coverage.reportPaths"  -> (baseDirectory.value / "target" / "scala-3.7.0" / "scoverage-report" / "scoverage.xml").getPath,
        "sonar.scala.scapegoat.reportPaths" -> (baseDirectory.value / "target" / "scala-3.7.0" / "scapegoat-report" / "scapegoat-scalastyle.xml").getPath,
        "sonar.sourceEncoding"              -> "UTF-8",
        "sonar.qualitygate.wait"            -> "true",
        "sonar.coverage.exclusions"         -> "**/Main.scala, **/*Config.scala, **/Syntax.scala, **/HttpServerOptions.scala"
      )
    )
  )

lazy val `profile-search` = (project in file("modules/profile-search"))
  .settings(scapegoatSettings)
  .dependsOn(common)
  .settings(
    Seq(
      sonarProperties ++= Map(
        "sonar.host.url"                    -> "https://sonar.persgroep.digital",
        "sonar.projectVersion"              -> version.value,
        "sonar.projectKey"                  -> "profile-service:profile-search",
        "sonar.projectName"                 -> "profile-search",
        "sonar.projectBaseDir"              -> baseDirectory.value.getPath,
        "sonar.scala.coverage.reportPaths"  -> (baseDirectory.value / "target" / "scala-3.7.0" / "scoverage-report" / "scoverage.xml").getPath,
        "sonar.scala.scapegoat.reportPaths" -> (baseDirectory.value / "target" / "scala-3.7.0" / "scapegoat-report" / "scapegoat-scalastyle.xml").getPath,
        "sonar.sourceEncoding"              -> "UTF-8",
        "sonar.qualitygate.wait"            -> "true",
        "sonar.coverage.exclusions"         -> "**/*Config.scala, **/Syntax.scala"
      )
    )
  ).settings(libraryDependencies ++= Seq(
    ProfileSearchDependencies.Tapir.`tapir-core`,
    ProfileSearchDependencies.Tapir.`tapir-http4s-server`,
    ProfileSearchDependencies.Tapir.`tapir-json-circe`,
    ProfileSearchDependencies.Tapir.`tapir-openapi-docs`,
    ProfileSearchDependencies.Tapir.`tapir-swagger-ui-bundle`,
    ProfileSearchDependencies.Tapir.`tapir-sttp-stub-server`,
    ProfileSearchDependencies.Sttp.`circe`,
    ProfileSearchDependencies.Sttp.`cats`,
    ProfileSearchDependencies.PureConfig.`pureconfig-core`,
    ProfileSearchDependencies.Logback.`logback-classic`,
    ProfileSearchDependencies.Logback.`logback-jackson`,
    ProfileSearchDependencies.Logback.`logback-json-classic`,
    ProfileSearchDependencies.Elastic4s.`elastic4s-client-esjava`,
    ProfileSearchDependencies.Elastic4s.`elastic4s-json-circe`,
    ProfileSearchDependencies.Elastic4s.`elastic4s-effect-cats`,
    ProfileSearchDependencies.TestContainers.`testcontainers`,
    ProfileSearchDependencies.TestContainers.`mysql`,
    ProfileSearchDependencies.Weaver.`weaver-cats`,
    ProfileSearchDependencies.Weaver.`weaver-scalacheck`,
    ProfileSearchDependencies.Doobie.`doobie-core`,
    ProfileSearchDependencies.Doobie.`doobie-hikari`,
    ProfileSearchDependencies.Doobie.`doobie-postgres`,
    ProfileSearchDependencies.Doobie.`doobie-postgres-circe`,
    ProfileSearchDependencies.Doobie.`doobie-scalatest`,
    ProfileSearchDependencies.MySql.`mysql-connector-j`,
    ProfileSearchDependencies.Log4Cats.`log4cats-slf4j`,
    ProfileSearchDependencies.Log4Cats.`log4cats-testing`,
    ProfileSearchDependencies.WebshopProduct.`webshop-product`,
    ProfileSearchDependencies.Chimney.`chimney`,
    ProfileSearchDependencies.Chimney.`chimney-cats`,
    ProfileSearchDependencies.ScalaMock.`scalamock`,
    ProfileSearchDependencies.ScalaMock.`scalamock-cats-effect`,
    ProfileSearchDependencies.CatsEffectTest.`cats-effect-testkit`,
    ProfileSearchDependencies.CatsEffectTest.`cats-effect-testing-scalatest`
  ))

lazy val `recruiter-favorites` = (project in file("modules/recruiter-favorites"))
  .settings(scapegoatSettings)
  .dependsOn(common)
  .settings(
    Seq(
      sonarProperties ++= Map(
        "sonar.host.url"                    -> "https://sonar.persgroep.digital",
        "sonar.projectVersion"              -> version.value,
        "sonar.projectKey"                  -> "profile-service:recruiter-favorites",
        "sonar.projectName"                 -> "recruiter-favorites",
        "sonar.projectBaseDir"              -> baseDirectory.value.getPath,
        "sonar.scala.coverage.reportPaths"  -> (baseDirectory.value / "target" / "scala-3.7.0" / "scoverage-report" / "scoverage.xml").getPath,
        "sonar.scala.scapegoat.reportPaths" -> (baseDirectory.value / "target" / "scala-3.7.0" / "scapegoat-report" / "scapegoat-scalastyle.xml").getPath,
        "sonar.sourceEncoding"              -> "UTF-8",
        "sonar.qualitygate.wait"            -> "true",
        "sonar.coverage.exclusions"         -> "**/*Config.scala"
      )
    )
  ).settings(libraryDependencies ++= Seq(
    RecruiterFavoritesDependencies.Sttp.`cats`,
    RecruiterFavoritesDependencies.PureConfig.`pureconfig-core`,
    RecruiterFavoritesDependencies.Chimney.`chimney`,
    RecruiterFavoritesDependencies.Chimney.`chimney-cats`,
    RecruiterFavoritesDependencies.Logback.`logback-classic`,
    RecruiterFavoritesDependencies.Logback.`logback-jackson`,
    RecruiterFavoritesDependencies.Logback.`logback-json-classic`,
    RecruiterFavoritesDependencies.Jackson.`jackson-databind`,
    RecruiterFavoritesDependencies.TestContainers.`testcontainers`,
    RecruiterFavoritesDependencies.TestContainers.`mysql`,
    RecruiterFavoritesDependencies.Weaver.`weaver-cats`,
    RecruiterFavoritesDependencies.Weaver.`weaver-scalacheck`,
    RecruiterFavoritesDependencies.Doobie.`doobie-core`,
    RecruiterFavoritesDependencies.Doobie.`doobie-hikari`,
    RecruiterFavoritesDependencies.Doobie.`doobie-postgres`,
    RecruiterFavoritesDependencies.Doobie.`doobie-postgres-circe`,
    RecruiterFavoritesDependencies.Doobie.`doobie-scalatest`,
    RecruiterFavoritesDependencies.MySql.`mysql-connector-j`,
    RecruiterFavoritesDependencies.Log4Cats.`log4cats-slf4j`,
    RecruiterFavoritesDependencies.Log4Cats.`log4cats-testing`,
    RecruiterFavoritesDependencies.ScalaMock.`scalamock`,
    RecruiterFavoritesDependencies.ScalaMock.`scalamock-cats-effect`,
    RecruiterFavoritesDependencies.CatsEffectTest.`cats-effect-testkit`,
    RecruiterFavoritesDependencies.CatsEffectTest.`cats-effect-testing-scalatest`
  ))

lazy val `saved-search` = (project in file("modules/saved-search"))
  .settings(scapegoatSettings)
  .dependsOn(common, `profile-search`)
  .settings(
    Seq(
      sonarProperties ++= Map(
        "sonar.host.url"                    -> "https://sonar.persgroep.digital",
        "sonar.projectVersion"              -> version.value,
        "sonar.projectKey"                  -> "profile-service:saved-search",
        "sonar.projectName"                 -> "saved-search",
        "sonar.projectBaseDir"              -> baseDirectory.value.getPath,
        "sonar.scala.coverage.reportPaths"  -> (baseDirectory.value / "target" / "scala-3.7.0" / "scoverage-report" / "scoverage.xml").getPath,
        "sonar.scala.scapegoat.reportPaths" -> (baseDirectory.value / "target" / "scala-3.7.0" / "scapegoat-report" / "scapegoat-scalastyle.xml").getPath,
        "sonar.sourceEncoding"              -> "UTF-8",
        "sonar.qualitygate.wait"            -> "true"
      )
    )
  ).settings(libraryDependencies ++= Seq(
    SavedSearchDependencies.Weaver.`weaver-cats`,
    SavedSearchDependencies.Weaver.`weaver-scalacheck`,
    SavedSearchDependencies.Doobie.`doobie-core`,
    SavedSearchDependencies.Doobie.`doobie-hikari`,
    SavedSearchDependencies.Doobie.`doobie-postgres`,
    SavedSearchDependencies.Doobie.`doobie-postgres-circe`,
    SavedSearchDependencies.Doobie.`doobie-scalatest`,
    SavedSearchDependencies.MySql.`mysql-connector-j`,
    SavedSearchDependencies.Log4Cats.`log4cats-slf4j`,
    SavedSearchDependencies.Log4Cats.`log4cats-testing`,
    SavedSearchDependencies.TestContainers.`testcontainers`,
    SavedSearchDependencies.TestContainers.`mysql`,
    SavedSearchDependencies.PureConfig.`pureconfig-core`,
    SavedSearchDependencies.Circe.`circe-literal`
  ))

lazy val common = (project in file("modules/common"))
  .settings(scapegoatSettings)
  .settings(
    Seq(
      sonarProperties ++= Map(
        "sonar.host.url"                    -> "https://sonar.persgroep.digital",
        "sonar.projectVersion"              -> version.value,
        "sonar.projectKey"                  -> "profile-service:common",
        "sonar.projectName"                 -> "common",
        "sonar.projectBaseDir"              -> baseDirectory.value.getPath,
        "sonar.scala.coverage.reportPaths"  -> (baseDirectory.value / "target" / "scala-3.7.0" / "scoverage-report" / "scoverage.xml").getPath,
        "sonar.scala.scapegoat.reportPaths" -> (baseDirectory.value / "target" / "scala-3.7.0" / "scapegoat-report" / "scapegoat-scalastyle.xml").getPath,
        "sonar.sourceEncoding"              -> "UTF-8",
        "sonar.qualitygate.wait"            -> "true"
      )
    )
  )

lazy val scapegoatSettings = Seq(
  ThisBuild / scapegoatVersion := "3.1.9",
  scapegoatDisabledInspections := Seq("ComparingUnrelatedTypes"),
  resolvers ++= Seq(
    "snapshots" at "https://archiva.persgroep.digital/repository/snapshots",
    "internal" at "https://archiva.persgroep.digital/repository/internal",
    "releases" at "https://archiva.persgroep.digital/repository/releases"
  )
)

lazy val dockerSettings = Seq(
  dockerExposedPorts     := Seq(14200),
  dockerRepository       := Some("************.dkr.ecr.eu-west-1.amazonaws.com"),
  dockerAliases ++= Set(
    dockerAlias.value.withTag(Some("latest")),
    dockerAlias.value.withTag(Some(scala.util.Properties.propOrNone("version").getOrElse("latest")))
  ).toSeq,
  Docker / daemonUserUid := Some("1001"),
  Docker / daemonUser    := "daemon",
  dockerBaseImage        := "eclipse-temurin:21-jre",
  Docker / maintainer    := maintainer.value,
  Docker / packageName   := "profile-service",
  Docker / version       := version.value,
  dockerCommands         := dockerCommands.value.filterNot {
    case Cmd("USER", args @ _*) if args contains "1001:0" => true
    case Cmd("USER", args @ _*) if args contains "daemon" => true
    case ExecCmd("ENTRYPOINT", _)                         => true
    case ExecCmd("CMD", args @ _*) if args.isEmpty        => true
    case _                                                => false
  },
  dockerCommands ++= Seq(
    Cmd("USER", (Docker / daemonUser).value),
    ExecCmd("RUN", "chmod", "755", "/opt/docker/bin/profile-service"),
    ExecCmd("ENTRYPOINT", "/opt/docker/bin/profile-service"),
    ExecCmd("CMD")
  )
)

testFrameworks += new TestFramework("weaver.framework.CatsEffect")
