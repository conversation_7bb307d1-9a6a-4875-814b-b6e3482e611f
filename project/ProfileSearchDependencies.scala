import sbt.*

object ProfileSearchDependencies {

  object Http4s {
    val Http4sVersion = "0.23.17"

    val `http4s-blaze-server` = "org.http4s" %% "http4s-blaze-server" % Http4sVersion
    val `http4s-blaze-client` = "org.http4s" %% "http4s-blaze-client" % Http4sVersion
  }

  object Tapir {
    val TapirVersion = "1.11.38"

    val `tapir-core` = "com.softwaremill.sttp.tapir"              %% "tapir-core"              % TapirVersion
    val `tapir-http4s-server` = "com.softwaremill.sttp.tapir"     %% "tapir-http4s-server"     % TapirVersion
    val `tapir-json-circe` = "com.softwaremill.sttp.tapir"        %% "tapir-json-circe"        % TapirVersion
    val `tapir-openapi-docs` = "com.softwaremill.sttp.tapir"      %% "tapir-openapi-docs"      % TapirVersion
    val `tapir-swagger-ui-bundle` = "com.softwaremill.sttp.tapir" %% "tapir-swagger-ui-bundle" % TapirVersion
    val `tapir-sttp-stub-server` = "com.softwaremill.sttp.tapir"  %% "tapir-sttp-stub-server"  % TapirVersion
  }

  object Sttp {
    val SttpVersion = "3.11.0"

    val `circe` = "com.softwaremill.sttp.client3" %% "circe" % SttpVersion
    val `cats` = "com.softwaremill.sttp.client3"  %% "cats"  % SttpVersion
  }

  object PureConfig {
    val PureConfigVersion = "0.17.9"

    val `pureconfig-core` = "com.github.pureconfig" %% "pureconfig-core" % PureConfigVersion
  }

  object Logback {
    val LogbackVersion = "1.5.18"
    val LogbackContribVersion = "0.1.5"

    val `logback-classic` = "ch.qos.logback" % "logback-classic" % LogbackVersion
    val `logback-json-classic` =
      "ch.qos.logback.contrib" % "logback-json-classic" % LogbackContribVersion excludeAll ExclusionRule("ch.qos.logback")
    val `logback-jackson` = "ch.qos.logback.contrib" % "logback-jackson" % LogbackContribVersion exclude ("ch.qos.logback", "logback-core")
  }

  object Elastic4s {
    val ElasticSearchVersion = "8.18.2"
    val Elastic4sCirceVersion = "8.11.5"

    val `elastic4s-client-esjava` = "nl.gn0s1s"            %% "elastic4s-client-esjava" % ElasticSearchVersion
    val `elastic4s-json-circe` = "com.sksamuel.elastic4s"  %% "elastic4s-json-circe"    % Elastic4sCirceVersion
    val `elastic4s-effect-cats` = "com.sksamuel.elastic4s" %% "elastic4s-effect-cats"   % Elastic4sCirceVersion
  }

  object TestContainers {
    val TestContainerVersion = "1.21.3"

    val `testcontainers` = "org.testcontainers" % "testcontainers" % TestContainerVersion % Test
    val `mysql` = "org.testcontainers"          % "mysql"          % TestContainerVersion % Test
  }

  object Weaver {
    val WeaverVersion = "0.9.2"

    val `weaver-cats` = "org.typelevel"       %% "weaver-cats"       % WeaverVersion % Test
    val `weaver-scalacheck` = "org.typelevel" %% "weaver-scalacheck" % WeaverVersion % Test
  }

  object Doobie {
    val DoobieVersion = "1.0.0-RC10"

    val `doobie-core` = "org.tpolecat"           %% "doobie-core"           % DoobieVersion
    val `doobie-hikari` = "org.tpolecat"         %% "doobie-hikari"         % DoobieVersion
    val `doobie-postgres` = "org.tpolecat"       %% "doobie-postgres"       % DoobieVersion
    val `doobie-postgres-circe` = "org.tpolecat" %% "doobie-postgres-circe" % DoobieVersion
    val `doobie-scalatest` = "org.tpolecat"      %% "doobie-scalatest"      % DoobieVersion % Test
  }

  object MySql {
    val MySqlVersion = "9.3.0"

    val `mysql-connector-j` = "com.mysql" % "mysql-connector-j" % MySqlVersion
  }

  object Log4Cats {
    val Log4CatsVersion = "2.7.1"

    val `log4cats-slf4j` = "org.typelevel"   %% "log4cats-slf4j"   % Log4CatsVersion
    val `log4cats-testing` = "org.typelevel" %% "log4cats-testing" % Log4CatsVersion % Test
  }

  object WebshopProduct {
    val `webshop-product` = "nl.dpgr.webshop-product" %% "webshop-product" % "129"
  }

  object Chimney {
    val ChimneyVersion = "1.8.2"

    val `chimney` = "io.scalaland"      %% "chimney"      % ChimneyVersion
    val `chimney-cats` = "io.scalaland" %% "chimney-cats" % ChimneyVersion
  }

  object ScalaMock {
    val ScalaMockVersion = "7.4.0"

    val `scalamock` = "org.scalamock"             %% "scalamock"             % ScalaMockVersion
    val `scalamock-cats-effect` = "org.scalamock" %% "scalamock-cats-effect" % ScalaMockVersion
  }

  object CatsEffectTest {
    val CatsEffectVersion = "3.6.3"
    val CatsEffectTestingVersion = "1.6.0"

    val `cats-effect-testkit` = "org.typelevel"           %% "cats-effect-testkit"           % CatsEffectVersion        % Test
    val `cats-effect-testing-scalatest` = "org.typelevel" %% "cats-effect-testing-scalatest" % CatsEffectTestingVersion % Test
  }
}
