package nl.dpes.savedsearch.storage

import cats.effect.*
import cats.effect.std.UUIDGen
import cats.implicits.*
import doobie.implicits.*
import doobie.Fragment
import doobie.util.transactor.Transactor
import io.circe.syntax.EncoderOps
import nl.dpes.savedsearch.config.SavedSearchConfig.TableName
import nl.dpes.savedsearch.domain.{Frequency, RecruiterId, SavedSearchFilters}
import nl.dpes.savedsearch.service.SavedSearchRepository
import io.scalaland.chimney.dsl.*

class MySqlSavedSearchRepository[F[_]: MonadCancelThrow: UUIDGen](tableName: TableName, xa: Transactor[F])
    extends SavedSearchRepository[F] {

  val savedSearchTable: Fragment = Fragment.const(tableName.value)

  override def initialize: F[Unit] =
    sql"""
       CREATE TABLE IF NOT EXISTS $savedSearchTable (
          id            VARCHAR(36)  NOT NULL,
          name          VARCHAR(100) NOT NULL,
          recruiterId   VARCHAR(36)  NOT NULL,
          filters       JSON         NOT NULL,
          frequency     VARCHAR(36)  NOT NULL,
          PRIMARY KEY (id),
          INDEX (recruiterId)
       )
      """.update.run.transact(xa).void

  override def create(recruiterId: RecruiterId, name: String, frequency: Frequency, filters: SavedSearchFilters): F[Unit] =
    for {
      id <- UUIDGen[F].randomUUID
      _ <-
        sql"""
      INSERT INTO $savedSearchTable (id, name, recruiterId, filters, frequency)
      VALUES (
        ${id.toString}, $name, ${recruiterId.into[dbmodel.RecruiterId].transform},
        ${filters.asJson.noSpaces}, ${frequency.into[dbmodel.Frequency].transform}
      )
    """.update.run.transact(xa).void
    } yield ()
}

object MySqlSavedSearchRepository {
  def resource[F[_]: MonadCancelThrow: UUIDGen](
    tableName: TableName,
    transactor: Transactor[F]
  ): Resource[F, SavedSearchRepository[F]] =
    Resource.eval(for {
      repo <- new MySqlSavedSearchRepository[F](tableName, transactor).pure
      _    <- repo.initialize
    } yield repo)

}
