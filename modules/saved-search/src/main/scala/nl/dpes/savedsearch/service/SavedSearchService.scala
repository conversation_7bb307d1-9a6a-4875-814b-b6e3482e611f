package nl.dpes.savedsearch.service

import cats.effect.Resource
import cats.effect.kernel.MonadCancelThrow
import nl.dpes.savedsearch.domain.*
import org.typelevel.log4cats.LoggerFactoryGen

trait SavedSearchService[F[_]] {
  def create(
    recruiterId: RecruiterId,
    name: String,
    frequency: Frequency,
    filters: SavedSearchFilters
  ): F[Unit]
}

object SavedSearchService {
  def resource[F[_]: {MonadCancelThrow, LoggerFactoryGen}](repository: SavedSearchRepository[F]): Resource[F, SavedSearchService[F]] =
    Resource.pure(new SavedSearchService[F] {
      override def create(
        recruiterId: RecruiterId,
        name: String,
        frequency: Frequency,
        filters: SavedSearchFilters
      ): F[Unit] = repository.create(recruiterId, name, frequency, filters)
    })
}
