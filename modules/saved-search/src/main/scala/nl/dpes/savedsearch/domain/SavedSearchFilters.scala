package nl.dpes.savedsearch.domain

import doobie.{Get, Read}
import nl.dpes.profilesearch.domain.*
import nl.dpes.profilesearch.domain.filter.*

case class SavedSearchFilters(
  searchTerm: Option[SearchTermFilter],
  city: Option[CityFilter],
  geoDistance: Option[GeoDistanceFilter],
  provinces: Seq[ProvinceFilter],
  updatedDate: Option[UpdateDateFilter],
  functionGroups: Option[FunctionGroupFilter],
  workLevels: Option[WorkLevelFilter],
  workingHours: Option[WorkingHourFilter],
  careerLevels: Option[CareerLevelFilter],
  requestedSalaries: Option[RequestedSalaryFilter],
  availabilities: Option[AvailabilityFilter],
  driversLicenses: Option[DriversLicenseFilter],
  languages: Option[LanguageFilter]
)

// todo: [Maybe] move it into a special object/class (domain -> json conversion)
object SavedSearchFilters {
  import io.circe.*
  import io.circe.generic.semiauto.*

  given Encoder[SearchTerm] = deriveEncoder
  given Encoder[SearchTermFilter] = deriveEncoder
  given Encoder[City] = (value: City) => Json.fromString(value.toString)
  given Encoder[CityFilter] = deriveEncoder
  given Encoder[GeoDistanceFilter] = deriveEncoder
  given Encoder[Province] = (value: Province) => Json.fromString(value.toString)
  given Encoder[ProvinceFilter] = deriveEncoder
  given Encoder[UpdateDate] = deriveEncoder
  given Encoder[UpdateDateFilter] = deriveEncoder
  given Encoder[FunctionGroup] = (value: FunctionGroup) => Json.fromString(value.toString)
  given Encoder[FunctionGroupFilter] = deriveEncoder
  given Encoder[WorkLevel] = (value: WorkLevel) => Json.fromString(value.toString)
  given Encoder[WorkLevelFilter] = deriveEncoder
  given Encoder[WorkingHours] = (value: WorkingHours) => Json.fromString(WorkingHours.toEntryName(value))
  given Encoder[WorkingHourFilter] = deriveEncoder
  given Encoder[CareerLevel] = (value: CareerLevel) => Json.fromString(value.toString)
  given Encoder[CareerLevelFilter] = deriveEncoder
  given Encoder[RequestedSalary] = (value: RequestedSalary) => Json.fromString(value.toString)
  given Encoder[RequestedSalaryFilter] = deriveEncoder
  given Encoder[Availability] = (value: Availability) => Json.fromString(value.toString)
  given Encoder[AvailabilityFilter] = deriveEncoder
  given Encoder[DriversLicense] = (value: DriversLicense) => Json.fromString(value.toString)
  given Encoder[DriversLicenseFilter] = deriveEncoder
  given Encoder[Language] = (value: Language) => Json.fromString(value.toString)
  given Encoder[LanguageFilter] = deriveEncoder

  given Encoder[SavedSearchFilters] = deriveEncoder

  given Decoder[SearchTerm] = deriveDecoder
  given Decoder[SearchTermFilter] = deriveDecoder
  given Decoder[City] = Decoder.decodeString.map(City(_))
  given Decoder[CityFilter] = deriveDecoder
  given Decoder[GeoDistanceFilter] = deriveDecoder
  given Decoder[Province] = Decoder.decodeString.map(Province(_))
  given Decoder[ProvinceFilter] = deriveDecoder
  given Decoder[UpdateDate] = deriveDecoder
  given Decoder[UpdateDateFilter] = deriveDecoder
  given Decoder[FunctionGroup] = Decoder.decodeString.map(FunctionGroup(_))
  given Decoder[FunctionGroupFilter] = deriveDecoder
  given Decoder[WorkLevel] = Decoder.decodeString.map(WorkLevel(_))
  given Decoder[WorkLevelFilter] = deriveDecoder
  given Decoder[WorkingHours] = Decoder.decodeString.emap(WorkingHours.fromString(_).toRight("Invalid WorkingHours value"))
  given Decoder[WorkingHourFilter] = deriveDecoder
  given Decoder[CareerLevel] = Decoder.decodeString.map(CareerLevel(_))
  given Decoder[CareerLevelFilter] = deriveDecoder
  given Decoder[RequestedSalary] = Decoder.decodeString.emap(RequestedSalary.fromString(_).toRight("Invalid RequestedSalary value"))
  given Decoder[RequestedSalaryFilter] = deriveDecoder
  given Decoder[Availability] = Decoder.decodeString.emap(Availability.fromString(_).toRight("Invalid Availability value"))
  given Decoder[AvailabilityFilter] = deriveDecoder
  given Decoder[DriversLicense] = Decoder.decodeString.map(DriversLicense(_))
  given Decoder[DriversLicenseFilter] = deriveDecoder
  given Decoder[Language] = Decoder.decodeString.map(Language(_))
  given Decoder[LanguageFilter] = deriveDecoder

  given Decoder[SavedSearchFilters] = deriveDecoder
}
