package nl.dpes.savedsearch.storage.dbmodel

enum Frequency {
  case Daily, Weekly
}

object Frequency {
  def fromString(value: String): Option[Frequency] = value.toLowerCase match {
    case "daily"  => Some(Daily)
    case "weekly" => Some(Weekly)
    case _        => None
  }

  given doobie.Meta[Frequency] =
    doobie.Meta[String].tiemap(string => fromString(string).toRight(s"Unknown value for frequency: $string"))(_.toString)
}
