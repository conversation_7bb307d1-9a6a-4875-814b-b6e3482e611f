package nl.dpes.savedsearch.storage.dbmodel

import nl.dpes.savedsearch.domain
import io.scalaland.chimney.Transformer

opaque type RecruiterId = String

object RecruiterId {
  def apply(value: String): RecruiterId = value

  given doobie.Meta[RecruiterId] = doobie.Meta.StringMeta
//  given Transformer[RecruiterId, domain.RecruiterId] = id => nl.dpes.savedsearch.domain.RecruiterId(id)
  given Transformer[domain.RecruiterId, RecruiterId] = id => RecruiterId(id.value)
}
