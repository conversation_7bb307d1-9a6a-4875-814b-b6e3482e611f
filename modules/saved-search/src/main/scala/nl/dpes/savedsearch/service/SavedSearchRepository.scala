package nl.dpes.savedsearch.service

import cats.effect.*
import cats.implicits.*
import doobie.implicits.*
import doobie.Fragment
import doobie.util.transactor.Transactor
import nl.dpes.savedsearch.config.SavedSearchConfig.TableName
import nl.dpes.savedsearch.domain.{Frequency, RecruiterId, SavedSearchFilters}

trait SavedSearchRepository[F[_]] {
  def initialize: F[Unit]
  def create(recruiterId: RecruiterId, name: String, frequency: Frequency, filters: SavedSearchFilters): F[Unit]
}
