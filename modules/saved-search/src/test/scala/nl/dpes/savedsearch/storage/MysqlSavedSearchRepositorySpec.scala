package nl.dpes.savedsearch.storage

import cats.effect.{IO, Resource}
import doobie.implicits.*
import doobie.{Fragment, Get, Transactor}
import io.scalaland.chimney.dsl.into
import nl.dpes.profilesearch.domain.SearchTerm
import nl.dpes.profilesearch.domain.filter.SearchTermFilter
import nl.dpes.savedsearch.config.SavedSearchConfig.TableName
import nl.dpes.savedsearch.domain.{Frequency, RecruiterId, SavedSearchFilters}
import nl.dpes.savedsearch.domain.Frequency.Daily
import nl.dpes.savedsearch.service.SavedSearchRepository
import nl.dpes.savedsearch.storage.MySqlSavedSearchRepository
import nl.dpes.savedsearch.testcontainers.MySqlDatabaseGenerator
import nl.dpes.savedsearch.testcontainers.MySqlDatabaseGenerator.WrappedTransactor
import weaver.{GlobalRead, IOSuite}

import java.util.UUID

class MysqlSavedSearchRepositorySpec(global: GlobalRead) extends IOSuite with MySqlDatabaseGenerator {

  override type Res = Transactor[IO]

  override def sharedResource: Resource[IO, Res] =
    global.getOrFailR[WrappedTransactor]().map(_.transactor)

  case class Repo(savedSearchRepo: SavedSearchRepository[IO], tableName: TableName)

  def randomRepo(xa: Transactor[IO]): Resource[IO, Repo] = {
    val id = UUID.randomUUID().toString.replace("-", "")
    for {
      repo <- MySqlSavedSearchRepository.resource(TableName(s"saved_search_$id"), xa)
    } yield Repo(repo, TableName(s"saved_search_$id"))
  }

  test("It should be able to create the table in an idempotent way") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _       <- repo.savedSearchRepo.initialize.attempt
        result  <- repo.savedSearchRepo.initialize.attempt
        columns <- getColumnNames(Fragment.const(repo.tableName.value), xa)
      } yield expect(
        result == Right(()) &&
          columns.sorted == List(
            "filters",
            "frequency",
            "id",
            "name",
            "recruiterId"
          )
      )
    }
  }

  given Get[SavedSearchFilters] = Get[String].temap(str => io.circe.parser.decode[SavedSearchFilters](str).left.map(_.getMessage))

  test("It should be able to create a saved search") { xa =>
    val recruiterId = RecruiterId("recruiter-123")
    val name = "My Saved Search"
    val filters = SavedSearchFilters(
      searchTerm = Some(SearchTermFilter(SearchTerm.unsafe("developer"))),
      city = None,
      geoDistance = None,
      provinces = Seq.empty,
      updatedDate = None,
      functionGroups = None,
      workLevels = None,
      workingHours = None,
      careerLevels = None,
      requestedSalaries = None,
      availabilities = None,
      driversLicenses = None,
      languages = None
    )

    randomRepo(xa).use { repo =>
      for {
        _ <- repo.savedSearchRepo.initialize
        _ <- repo.savedSearchRepo.create(
          recruiterId = recruiterId,
          name = name,
          frequency = Frequency.Daily,
          filters = filters
        )
        savedSearches <- getSavedSearches(recruiterId, xa, repo)
      } yield expect.same(1, savedSearches.size) &&
        expect.same(name, savedSearches.head._1) &&
        expect.same(Daily, savedSearches.head._2) &&
        expect.same(
          filters,
          savedSearches.head._3
        )
    }
  }

  def getColumnNames(tableName: Fragment, xa: Transactor[IO]): IO[List[String]] =
    sql"""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = '$tableName'
       """
      .query[String]
      .to[List]
      .transact(xa)

  def getSavedSearches(recruiterId: RecruiterId, xa: Transactor[IO], repo: Repo): IO[List[(String, Frequency, SavedSearchFilters)]] =
    sql"""
        SELECT name, frequency, filters
        FROM ${Fragment.const(repo.tableName.value)}
        WHERE recruiterId = ${recruiterId.into[dbmodel.RecruiterId].transform}
       """
      .query[(String, dbmodel.Frequency, SavedSearchFilters)]
      .to[List]
      .transact(xa)
      .map(_.map { case (name, frequency, filters) =>
        (name, frequency.into[Frequency].transform, filters)
      })
}
