package nl.dpes.savedsearch.storage.domain

import nl.dpes.savedsearch.storage.dbmodel.Frequency
import weaver.FunSuite

object FrequencySpec extends FunSuite {
  test("Frequency should be parsed from string") {
    expect.same(Some(Frequency.Daily), Frequency.fromString("daily"))
    expect.same(Some(Frequency.Weekly), Frequency.fromString("weekly"))
    expect.same(None, Frequency.fromString("unknown"))
  }
}
