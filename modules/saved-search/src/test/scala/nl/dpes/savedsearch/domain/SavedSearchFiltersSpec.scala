package nl.dpes.savedsearch.domain

import cats.data.NonEmptyList
import io.circe.literal.json
import io.circe.syntax.EncoderOps
import weaver.FunSuite
import nl.dpes.profilesearch.domain.*
import nl.dpes.profilesearch.domain.filter.*

object SavedSearchFiltersSpec extends FunSuite {
  test("Saved search filters should be encoded correctly") {
    val filters = SavedSearchFilters(
      searchTerm = Some(SearchTermFilter(SearchTerm.unsafe("developer"))),
      city = Some(CityFilter(City("Amsterdam"))),
      geoDistance = Some(GeoDistanceFilter(10, 20)),
      provinces = Seq(ProvinceFilter(NonEmptyList.of(Province("North Holland")))),
      updatedDate = Some(UpdateDateFilter(UpdateDate.All)),
      functionGroups = Some(FunctionGroupFilter(NonEmptyList.of(FunctionGroup("Engineering")))),
      workLevels = Some(WorkLevelFilter(NonEmptyList.of(WorkLevel("Full-time")))),
      workingHours = Some(WorkingHourFilter(NonEmptyList.of(WorkingHours.From32To40))),
      careerLevels = Some(CareerLevelFilter(NonEmptyList.of(CareerLevel("Senior")))),
      requestedSalaries = Some(RequestedSalaryFilter(NonEmptyList.of(RequestedSalary.MoreThan7000))),
      availabilities = Some(AvailabilityFilter(NonEmptyList.of(Availability.PerDirect))),
      driversLicenses = Some(DriversLicenseFilter(NonEmptyList.of(DriversLicense("A")))),
      languages = Some(LanguageFilter(NonEmptyList.of(Language("English"))))
    )

    val expectedJson =
      json"""
            {
              "searchTerm":{"query":{"value":"developer"}},
              "city":{"city":"Amsterdam"},
              "geoDistance":{"latitude":10.0,"longitude":20.0},
              "provinces":[{"provinces":["North Holland"]}],
              "updatedDate":{"updateDate":{"All":{}}},
              "functionGroups":{"functionGroups":["Engineering"]},
              "workLevels":{"workLevels":["Full-time"]},
              "workingHours":{"workingHours":["32 tot en met 40 uur"]},
              "careerLevels":{"values":["Senior"]},
              "requestedSalaries":{"salaries":["> 7.000"]},
              "availabilities":{"values":["Per direct"]},
              "driversLicenses":{"driversLicenses":["A"]},
              "languages":{"languages":["English"]}
            }
      """.noSpaces

    expect.same(expectedJson, filters.asJson.noSpaces)
  }
}
