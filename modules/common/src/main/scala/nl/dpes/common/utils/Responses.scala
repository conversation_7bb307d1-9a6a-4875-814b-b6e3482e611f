package nl.dpes.common.utils

object Responses {

  sealed abstract class ErrorMessage(message: String) extends Throwable(message)

  case class BadRequest(message: String) extends ErrorMessage(s"BadRequest: $message")
  case class NotFound(message: String) extends ErrorMessage(s"Not found: $message")
  case class Forbidden(message: String) extends ErrorMessage(s"Forbidden: $message")
  case class TooManyRequests(message: String) extends ErrorMessage(s"Too Many Requests: $message")
  case class Timeout(message: String) extends ErrorMessage(s"Timeout: $message")
}
