package nl.dpes.profilesearch.mapper.aggregation

import com.sksamuel.elastic4s.requests.searches.aggs.TermsAggregation
import nl.dpes.profilesearch.domain
import nl.dpes.profilesearch.domain.Aggregation.UpdatedDate
import nl.dpes.profilesearch.service.model.dbmodel

object AllAggregations {
  def getAllTermAggregations: List[TermsAggregation] =
    domain.Aggregation.values.toList
      .filterNot(_ == UpdatedDate)
      .map(aggregation =>
        TermsAggregation(name = aggregation.name, field = Some(dbmodel.Aggregation.fieldNameForAggregation(aggregation)), size = Some(30))
      )
}
