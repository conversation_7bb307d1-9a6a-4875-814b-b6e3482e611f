package nl.dpes.profilesearch.mapper
package aggregation

import cats.Monad
import cats.effect.kernel.Clock
import cats.implicits.*
import cats.syntax.option.given
import cats.syntax.traverse.given
import com.sksamuel.elastic4s.ElasticApi.{boolQuery, filterAgg, globalAggregation, matchAllQuery}
import com.sksamuel.elastic4s.requests.searches.aggs.{Aggregation, RangeAggregation, TermsAggregation}
import com.sksamuel.elastic4s.requests.searches.queries.compound.BoolQuery
import nl.dpes.profilesearch.domain.{Aggregation as DomainAggregation, *}
import nl.dpes.profilesearch.domain.filter.*
import nl.dpes.profilesearch.service.model.dbmodel.Aggregation.fieldNameForAggregation

import java.time.Instant
import scala.reflect.ClassTag

class FilterToAggregationMapper[F[_]: Clock: Monad](filterMapper: FilterMapper[F], filterExclusion: FilterExclusion) {
  private val globalAggregationName = "aggregations"
  private val commonAggregationPath = "aggregation"

  def getAggregations(filters: Seq[Filter]): F[List[Aggregation]] =
    for {
      globalFilterSubAggregations <- getGlobalFilterSubAggregations(filters)
    } yield List(
      globalAggregation(globalAggregationName)
        .subAggregations(globalFilterSubAggregations)
    )

  private def getGlobalFilterSubAggregations(filters: Seq[Filter]): F[List[Aggregation]] =
    for {
      subAggregations <- getSubAggregations
      result <- subAggregations
        .map { d =>
          val (domainAggregation, elasticAggregation) = d
          getFilterAggregation(domainAggregation, elasticAggregation, filters)
        }
        .toList
        .sequence
    } yield result

  private def getFilterAggregation(
    domainAggregation: DomainAggregation,
    elasticAggregation: Aggregation,
    filters: Seq[Filter]
  ): F[Aggregation] = {
    val aggregationFilters = filterExclusion.getFiltersForAggregation(domainAggregation, filters)
    for {
      query <- createQueriesFromFilters(aggregationFilters.toList)
    } yield filterAgg(domainAggregation.name, query).subAggregations(elasticAggregation)
  }

  private def createQueriesFromFilters(filters: List[Filter]): F[BoolQuery] =
    filters
      .traverse(filterMapper.mapToQuery)
      .map(_.flatten.toList)
      .map { queries =>
        val mustQuery = if (queries.isEmpty) List(matchAllQuery()) else queries
        boolQuery().must(mustQuery)
      }

  // todo: consider using individual aggregations for each filter
  private def getSubAggregations = for {
    availability <- getTermAggregation(
      DomainAggregation.Availability,
      values = Availability.values.map(_.entryName).toList.some
    )
    careerLevel <- getTermAggregation(
      DomainAggregation.CareerLevel
    )
    driverLicense <- getTermAggregation(
      DomainAggregation.DriverLicense
    )
    functionGroup <- getTermAggregation(
      DomainAggregation.FunctionGroup
    )
    language <- getTermAggregation(
      DomainAggregation.Language
    )
    province        <- getTermAggregation(DomainAggregation.Province)
    requestedSalary <- getTermAggregation(DomainAggregation.RequestedSalary)
    updatedDate     <- getUpdateDateAggregation
    workingHours <- getTermAggregation(
      DomainAggregation.WorkingHours,
      values = WorkingHours.values.map(WorkingHours.toEntryName).toList.some
    )
    workLevel <- getTermAggregation(
      DomainAggregation.WorkLevel
    )
  } yield Map(
    DomainAggregation.Availability    -> availability,
    DomainAggregation.CareerLevel     -> careerLevel,
    DomainAggregation.DriverLicense   -> driverLicense,
    DomainAggregation.FunctionGroup   -> functionGroup,
    DomainAggregation.Language        -> language,
    DomainAggregation.Province        -> province,
    DomainAggregation.RequestedSalary -> requestedSalary,
    DomainAggregation.UpdatedDate     -> updatedDate,
    DomainAggregation.WorkingHours    -> workingHours,
    DomainAggregation.WorkLevel       -> workLevel
  )

  private def getTermAggregation(
    aggregation: DomainAggregation,
    values: Option[Seq[String]] = None,
    size: Int = 30
  ): F[Aggregation] = {
    val termsAggregation = TermsAggregation(commonAggregationPath).field(fieldNameForAggregation(aggregation)).size(size)
    val result = values match {
      case Some(value) => termsAggregation.includeExactValues(value)
      case None        => termsAggregation
    }

    Monad[F].pure(result)
  }

  private def getUpdateDateAggregation: F[Aggregation] =
    for {
      now <- Clock[F].realTime.map(finiteDuration => Instant.ofEpochMilli(finiteDuration.toMillis))
      ranges = UpdateDate.values.sorted
        .map(updateDate => updateDate -> UpdateDate.toTimestamp(updateDate, now))
        .map { data =>
          val (updateDate, timestamp) = data
          (updateDate.entryName.some, timestamp.getEpochSecond.toDouble, now.getEpochSecond.toDouble)
        }
    } yield RangeAggregation(commonAggregationPath, field = fieldNameForAggregation(DomainAggregation.UpdatedDate).some, ranges = ranges)
}
