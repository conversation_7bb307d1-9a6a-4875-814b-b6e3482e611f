package nl.dpes.profilesearch
package service.lock

import doobie.*
import doobie.implicits.*
import doobie.util.*
import nl.dpes.profilesearch.service.lock.LockingResult.*

import java.util.UUID
import scala.util.control.NoStackTrace

trait LockService {
  def acquire(key: Key, id: UUID): ConnectionIO[LockingResult]
  def release(key: Key, id: UUID): ConnectionIO[Unit]
}

class MysqlLockService extends LockService {

  override def acquire(key: Key, id: UUID): ConnectionIO[LockingResult] =
    sql"""
      SELECT GET_LOCK($key, 5)
    """
      .query[Int]
      .option
      .map {
        case Some(1) => LockAcquired(key, id)
        case Some(0) => LockAlreadyHeld(key, id)
        case _       => LockAcquisitionFailed(key, id)
      }

  override def release(key: Key, id: UUID): ConnectionIO[Unit] =
    sql"""
      SELECT RELEASE_LOCK($key)
    """
      .query[Int]
      .option
      .void
}
