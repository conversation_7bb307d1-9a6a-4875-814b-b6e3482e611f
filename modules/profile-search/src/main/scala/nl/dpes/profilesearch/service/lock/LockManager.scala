package nl.dpes.profilesearch
package service.lock

import cats.effect.Resource
import cats.effect.kernel.Async
import cats.effect.std.{Mutex, Semaphore, UUIDGen}
import doobie.*
import doobie.implicits.*
import doobie.util.transactor.Transactor
import nl.dpes.profilesearch.service.lock.LockingResult.*

import java.util.UUID
import java.util.concurrent.TimeoutException
import scala.concurrent.duration.*

trait LockManager[F[_]] {
  def runInLock[A](key: Key, fa: F[A]): F[A]
}
object LockManager {
  def apply[F[_]: {Async, UUIDGen}](lockService: LockService, xa: Transactor[F], config: LockConfig): F[LockManager[F]] =
    for {
      semaphore <- Semaphore[F](config.maximumConcurrentLocks.value) // make sure not all connections are blocked by locks
      mutexes   <- Async[F].ref(Map.empty[Key, Mutex[F]])
    } yield new LockManager[F] {

      def lock(key: Key): Resource[F, UUID] =
        Resource.make(for {
          id            <- UUIDGen[F].randomUUID
          lockingResult <- lockService.acquire(key, id).transact(xa)
          _ <- lockingResult match {
            case e: LockingError => Async[F].raiseError(e)
            case _               => Async[F].pure(())
          }
        } yield id)(id => lockService.release(key, id).transact(xa) >> Async[F].sleep(100.millis))

      def retryInLock[A](key: Key, fa: F[A], retries: Int = 5): F[A] =
        xa.connect(xa.kernel)
          .use { connection =>
            // by keeping the connection in scope, we ensure that the lock is held for the duration of the transaction
            lock(key).use(_ => fa)
          }
          .handleErrorWith {
            case _: LockingError if retries > 0 => Async[F].sleep(100.millis) >> retryInLock(key, fa, retries - 1)
            case e                              => Async[F].raiseError(e)
          }

      def runInLock[A](key: Key, fa: F[A]): F[A] = semaphore.permit.use(_ =>
        for {
          newMutex <- Mutex[F]
          mutex <- mutexes.modify { m =>
            m.get(key) match {
              case Some(mutex) => (m, mutex)
              case None        => (m + (key -> newMutex), newMutex)
            }
          }
          result <- Async[F].timeout(mutex.lock.use(_ => retryInLock(key, fa)), config.lockTimeout).adaptError { case _: TimeoutException =>
            LockTimedOut(key, config.lockTimeout)
          }
        } yield result
      )
    }
}
