package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import nl.dpes.profilesearch.service.model.dbmodel.AggregationResult.CommonAggregation
import nl.dpes.profilesearch.service.model.dbmodel.FacetsResult.TermFacets

case class AllAggregationResult(
  availabilities: TermFacets,
  careerLevels: TermFacets,
  driverLicenses: TermFacets,
  functionGroups: TermFacets,
  languages: TermFacets,
  provinces: TermFacets,
  requestedSalaries: TermFacets,
  workingHours: TermFacets,
  workLevels: TermFacets
)

object AllAggregationResult {
  given Decoder[AllAggregationResult] = io.circe.generic.semiauto.deriveDecoder

  def toAggregationResult(
    allAggregationResult: AllAggregationResult
  ): AggregationResult =
    AggregationResult(
      AggregationResult.Aggregations(
        availabilities = CommonAggregation(allAggregationResult.availabilities),
        careerLevels = CommonAggregation(allAggregationResult.careerLevels),
        driverLicenses = CommonAggregation(allAggregationResult.driverLicenses),
        functionGroups = CommonAggregation(allAggregationResult.functionGroups),
        languages = CommonAggregation(allAggregationResult.languages),
        provinces = CommonAggregation(allAggregationResult.provinces),
        requestedSalaries = CommonAggregation(allAggregationResult.requestedSalaries),
        updatedDate = CommonAggregation(TermFacets(List.empty)),
        workingHours = CommonAggregation(allAggregationResult.workingHours),
        workLevels = CommonAggregation(allAggregationResult.workLevels)
      )
    )
}
