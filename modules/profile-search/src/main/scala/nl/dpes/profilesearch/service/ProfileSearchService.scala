package nl.dpes.profilesearch
package service

import cats.effect.{Async, Resource}
import com.sksamuel.elastic4s.*
import com.sksamuel.elastic4s.ElasticApi.{boolQuery, get, idsQuery, matchAllQuery, search, termsQuery}
import com.sksamuel.elastic4s.ElasticDsl.*
import com.sksamuel.elastic4s.requests.searches.SearchResponse
import com.sksamuel.elastic4s.requests.searches.aggs.Aggregation
import com.sksamuel.elastic4s.requests.searches.queries.PrefixQuery
import com.sksamuel.elastic4s.requests.searches.queries.compound.BoolQuery
import io.circe.parser.decode
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.domain
import nl.dpes.profilesearch.domain.filter.{Filter, FindableFilter}
import nl.dpes.profilesearch.mapper.FilterMapper
import nl.dpes.profilesearch.mapper.aggregation.{AllAggregations, FilterExclusion, FilterToAggregationMapper}
import nl.dpes.profilesearch.service.model.dbmodel
import nl.dpes.profilesearch.service.model.dbmodel.AggregationDecoder.*
import nl.dpes.profilesearch.service.model.dbmodel.DbResponse
import nl.dpes.profilesearch.service.model.dbmodel.FacetsResult.TermBucket
import nl.dpes.profilesearch.service.model.dbmodel.Profile.given
import nl.dpes.profilesearch.service.visitedprofiles.{AccessLogger, VisitedProfilesService}
import nl.dpes.profilesearch.domain.Error.*
import nl.dpes.profilesearch.service.lock.{Key, LockManager}
import nl.dpes.profilesearch.service.model.detailpage.Profile
import nl.dpes.profilesearch.service.model.profilesearch.*
import nl.dpes.profilesearch.service.model.profilesearch.sort.*
import nl.dpes.profilesearch.service.model.profilesearch.pagination.*
import nl.dpes.profilesearch.service.model.profilesearch.suggestions.{CityPrefix, CitySuggestion as SuggestedCity}
import nl.dpes.profilesearch.service.viewedprofiles.ViewedProfilesService
import org.typelevel.log4cats.LoggerFactory

import scala.util.control.NoStackTrace

trait ProfileSearchService[F[_]] {

  def getCitySuggestions(cityPrefix: CityPrefix): F[List[SuggestedCity]]

  def getProfiles(
    pagination: Pagination,
    filters: List[Filter] = List.empty,
    sortField: Option[SortField] = None,
    sortOrder: Option[SortOrder] = None,
    recruiterId: Option[RecruiterId] = None,
    withoutAggregations: Boolean = false
  ): F[SearchResult]

  def getProfilesById(ids: List[ProfileId], recruiterId: Option[RecruiterId]): F[SearchResult]

  def getProfile(recruiterId: RecruiterId, profileId: ProfileId): F[Profile]
}

object ProfileSearchService {
  private case class ProfileWithCounterResponse(profiles: List[dbmodel.Profile], totalNumberOfProfiles: Long)

  private case class SearchError(message: String) extends Throwable(message) with NoStackTrace

  def impl[F[+_]: {Async, Functor, Executor, LoggerFactory}](
    index: String
  )(
    elasticClient: ElasticClient,
    visitedProfilesService: VisitedProfilesService[F],
    viewedProfilesService: ViewedProfilesService[F],
    lockManager: LockManager[F]
  ): ProfileSearchService[F] =
    new ProfileSearchService[F] {

      override def getCitySuggestions(cityPrefix: CityPrefix): F[List[SuggestedCity]] = {
        val query = search(index)
          .query(PrefixQuery(SuggestedCity.fieldPath, cityPrefix.value, caseInsensitive = Some(true)))
          .size(10)

        elasticClient.execute(query).flatMap {
          case RequestSuccess(_, _, _, result) =>
            for {
              profiles <- decodeProfiles(result).map(_.profiles.transformInto[List[ProfileSearchResult]])
            } yield profiles.map(_.city).distinct.flatten.map(c => SuggestedCity(c.value))
          case RequestFailure(_, _, _, error) => SearchError(error.asException.getMessage).raiseError

        }
      }

      override def getProfiles(
        pagination: Pagination,
        filters: List[Filter],
        sortField: Option[SortField],
        sortOrder: Option[SortOrder],
        recruiterId: Option[RecruiterId],
        withoutAggregations: Boolean = false
      ): F[SearchResult] = {
        val allFilters = filters :+ FindableFilter

        for {
          from               <- ((pagination.pageNumber.value - 1) * pagination.pageSize.value).pure
          sorting            <- Sorting(sortField, sortOrder).pure
          queriesFromFilters <- createQueriesFromFilters(allFilters)
          aggregations       <- createCorrectAggregations(allFilters)
          result <- elasticClient
            .execute(
              constructProfileSearchRequest(index, pagination, from, sorting, queriesFromFilters, aggregations, withoutAggregations)
            )
            .flatMap {
              case RequestSuccess(_, _, _, result) => createSearchResult(result, withoutAggregations, recruiterId)
              case RequestFailure(_, body, _, error) =>
                val errorMessage = error.asException.getMessage
                val bodyMessage = body.map(message => s"and body: $message").getOrElse("")
                SearchError(s"Failed with: ${error.asException.getMessage} $bodyMessage").raiseError
            }
        } yield result
      }

      override def getProfile(recruiterId: RecruiterId, profileId: ProfileId): F[Profile] =
        (for {
          response     <- elasticClient.execute(get(index, profileId.value))
          responseBody <- Async[F].fromOption(response.body, GetProfileFailure(profileId, "No body in response"))
          profileResponse <- Async[F].fromEither(
            decode[DbResponse[dbmodel.Profile]](responseBody).leftMap(e => GetProfileFailure(profileId, e.getMessage))
          )
          profile <-
            if (profileResponse.found)
              Async[F].fromOption(profileResponse._source.map(_.transformInto[Profile]), ProfileNotFound(profileId))
            else ProfileNotFound(profileId).raiseError
          _ <- lockManager.runInLock(
            Key(s"${recruiterId.value}-${profileId.value}"),
            visitedProfilesService.registerProfileView(recruiterId, profileId)
          )
          _ <- viewedProfilesService.save(recruiterId, profileId).recoverWith { case thr: Throwable =>
            AccessLogger[F].profileViewStorageFailed(recruiterId, profileId, thr)
          }
        } yield profile).attempt.flatTap {
          case Left(value) => AccessLogger[F].profileViewFailed(recruiterId, profileId, value)
          case Right(_)    => AccessLogger[F].profileViewed(recruiterId, profileId)
        }.rethrow

      private def constructProfileSearchRequest(
        elasticIndexName: String,
        pagination: Pagination,
        from: Int,
        sorting: Sorting,
        queriesFromFilters: List[BoolQuery],
        aggregations: List[Aggregation],
        withoutAggregations: Boolean = false
      ) = {
        val searchQuery = search(elasticIndexName)
          .query(boolQuery().must(queriesFromFilters))
          .trackTotalHits(true)
          .size(pagination.pageSize.value)
          .sortBy(sorting.toElasticSort)
          .from(from)

        val searchRequestWithAggregations = searchQuery
          .aggregations(aggregations)

        if (withoutAggregations) {
          searchQuery
        } else {
          searchRequestWithAggregations
        }
      }

      private def createQueriesFromFilters(filters: List[Filter]): F[List[BoolQuery]] =
        filters
          .map(FilterMapper.default.mapToQuery)
          .sequence
          .map(_.flatten.map(query => boolQuery().must(query)))

      private def createCorrectAggregations(filters: List[Filter]): F[List[Aggregation]] =
        FilterToAggregationMapper[F](FilterMapper.default, FilterExclusion.default).getAggregations(filters)

      private def createSearchResult(
        response: SearchResponse,
        withoutAggregations: Boolean,
        recruiterId: Option[RecruiterId]
      ): F[SearchResult] =
        for {
          decodedResponse  <- decodeProfiles(response)
          profiles         <- decodedResponse.profiles.map(_.transformInto[ProfileSearchResult]).pure
          completeProfiles <- appendLastViewedDate(profiles, recruiterId)
          aggregations     <- if (withoutAggregations) Map.empty.pure[F] else decodeAggregationResult(response)
        } yield SearchResult(completeProfiles, aggregations, decodedResponse.totalNumberOfProfiles)

      private def appendLastViewedDate(
        profiles: List[ProfileSearchResult],
        recruiterId: Option[RecruiterId]
      ): F[List[ProfileSearchResult]] =
        profiles.traverse { profile =>
          for {
            viewDate <- recruiterId match {
              case Some(id) => viewedProfilesService.getLastViewedDate(id, profile.id)
              case None     => None.pure
            }
          } yield profile.withLastViewedDate(viewDate)
        }

      private def decodeProfiles(response: SearchResponse): F[ProfileWithCounterResponse] = {
        val totalHits: Long = response.hits.total.value

        response.hits.hits.toList
          .flatTraverse { hit =>
            decode[dbmodel.Profile](hit.sourceAsString) match {
              case Right(value) => List(value).pure[F]
              case Left(thr)    => thr.raiseError[F, List[dbmodel.Profile]]
            }
          }
          .map(profiles => ProfileWithCounterResponse(profiles, totalHits))
      }

      private def decodeAggregationResult(
        response: SearchResponse
      ): F[Map[AggregationKeyName, List[TermBucket]]] =
        for {
          aggregations <- decodeAggregationResultFromSearchResponse[F](response).map(
            _.map((k, v) => (AggregationKeyName(k.name), v.map(aggregationBucketToTermBucket)))
          )
          allAggregations <- getAllTermBuckets.map(
            _.map((k, v) => (AggregationKeyName(k.name), v.map(aggregationBucketToTermBucket)))
          )
        } yield addEmptyAggregations(aggregations, allAggregations)

      private def getAllTermBuckets: F[Map[domain.Aggregation, List[Aggregations]]] =
        elasticClient
          .execute {
            search(index)
              .query(boolQuery().must(matchAllQuery(), termsQuery("findable", true)))
              .aggregations(AllAggregations.getAllTermAggregations)
          }
          .flatMap {
            case RequestSuccess(_, _, _, result) => decodeAllAggregationResultFromSearchResponse(result)
            case RequestFailure(_, _, _, error)  => SearchError(error.asException.getMessage).raiseError
          }

      private def addEmptyAggregations(
        aggregations: Map[AggregationKeyName, List[TermBucket]],
        allAggregations: Map[AggregationKeyName, List[TermBucket]]
      ): Map[AggregationKeyName, List[TermBucket]] =
        aggregations.map { (key, actualTermBuckets) =>
          lazy val correctedBuckets: List[TermBucket] = {
            val allKeys = actualTermBuckets.map(_.`key`)
            allAggregations
              .getOrElse(key, List.empty)
              .map(allAggregationBucket =>
                allAggregationBucket
                  .copy(`doc_count` = actualTermBuckets.find(_.`key` == allAggregationBucket.`key`).map(_.`doc_count`).getOrElse(0))
              )
          }

          key -> {
            key.value match {
              case domain.Aggregation.UpdatedDate.name => actualTermBuckets
              case _                                   => correctedBuckets
            }
          }
        }

      override def getProfilesById(ids: List[ProfileId], recruiterId: Option[RecruiterId]): F[SearchResult] =
        for {
          response <- elasticClient
            .execute {
              search(index)
                .query(idsQuery(ids.map(_.value)))
                .trackTotalHits(true)
                .size(ids.size)
            }
          result <- response match {
            case RequestSuccess(_, _, _, result) => createSearchResultForProfile(result, recruiterId)
            case RequestFailure(_, _, _, error)  => SearchError(error.asException.getMessage).raiseError
          }
        } yield result.copy(profiles = result.profiles.sortByIds(ids))

      private def createSearchResultForProfile(response: SearchResponse, recruiterId: Option[RecruiterId]): F[SearchResult] =
        for {
          decodedResponse  <- decodeProfiles(response)
          profiles         <- decodedResponse.profiles.map(_.transformInto[ProfileSearchResult]).pure
          completeProfiles <- appendLastViewedDate(profiles, recruiterId)
        } yield SearchResult(completeProfiles, aggregations = Map.empty, decodedResponse.totalNumberOfProfiles)

    }

  def resource[F[+_]: {Async, Functor, Executor, LoggerFactory}](
    index: String,
    elasticClient: ElasticClient,
    visitedProfilesService: VisitedProfilesService[F],
    viewedProfilesService: ViewedProfilesService[F],
    lockManager: LockManager[F]
  ): Resource[F, ProfileSearchService[F]] =
    Resource.eval(Async[F].delay(impl(index)(elasticClient, visitedProfilesService, viewedProfilesService, lockManager)))
}
