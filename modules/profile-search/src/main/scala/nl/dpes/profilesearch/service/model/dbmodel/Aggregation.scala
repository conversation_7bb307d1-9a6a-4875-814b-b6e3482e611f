package nl.dpes.profilesearch.service.model.dbmodel

import nl.dpes.profilesearch.domain
import nl.dpes.profilesearch.domain.Aggregation.*

object Aggregation {
  def fieldNameForAggregation(aggregation: domain.Aggregation): String = aggregation match {
    case Availability    => "availability"
    case CareerLevel     => "careerLevel"
    case DriverLicense   => "driverLicenses.group"
    case FunctionGroup   => "functionGroups"
    case Language        => "languages.name"
    case Province        => "commute.province"
    case RequestedSalary => "requestedSalary"
    case UpdatedDate     => "updatedDate"
    case WorkingHours    => "workingHours"
    case WorkLevel       => "workLevels"
  }
}
