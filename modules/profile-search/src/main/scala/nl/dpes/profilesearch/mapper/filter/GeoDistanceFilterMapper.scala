package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.common.DistanceUnit
import com.sksamuel.elastic4s.requests.script.Script
import com.sksamuel.elastic4s.requests.searches.queries.{Query, ScriptQuery}
import com.sksamuel.elastic4s.requests.searches.queries.geo.GeoDistanceQuery
import nl.dpes.profilesearch.domain.filter.Filter

object GeoDistanceFilterMapper extends FilterToQueryMapper {

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = {
    case filter: nl.dpes.profilesearch.domain.filter.GeoDistanceFilter =>
      Seq(
        ScriptQuery(Script(s"""
            if (doc['commute.geoPoint'].size() == 0 || doc['commute.maxTravelDistance'].size() == 0) { return false; }
            double actualDistance = Math.round(doc['commute.geoPoint'].arcDistance(${filter.latitude}, ${filter.longitude})) / 1000.0;
            return actualDistance <= doc['commute.maxTravelDistance'].value;
          """))
      )
  }
}
