package nl.dpes.profilesearch.domain

enum Aggregation(val name: String) {
  case Availability extends Aggregation("availabilities")
  case CareerLevel extends Aggregation("careerLevels")
  case DriverLicense extends Aggregation("driverLicenses")
  case FunctionGroup extends Aggregation("functionGroups")
  case Language extends Aggregation("languages")
  case Province extends Aggregation("provinces")
  case RequestedSalary extends Aggregation("requestedSalaries")
  case UpdatedDate extends Aggregation("updatedDate")
  case WorkingHours extends Aggregation("workingHours")
  case WorkLevel extends Aggregation("workLevels")
}
