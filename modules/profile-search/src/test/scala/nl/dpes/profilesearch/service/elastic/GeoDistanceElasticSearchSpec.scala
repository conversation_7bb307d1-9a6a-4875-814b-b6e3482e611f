package nl.dpes.profilesearch.service.elastic

import cats.effect.IO
import cats.effect.kernel.Resource
import cats.implicits.*
import com.sksamuel.elastic4s.ElasticClient
import com.sksamuel.elastic4s.cats.effect.instances.*
import nl.dpes.profilesearch.service.ProfileSearchService
import nl.dpes.profilesearch.service.lock.{Key, LockManager}
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profilesearch.service.viewedprofiles.ViewedProfilesService
import nl.dpes.profilesearch.service.visitedprofiles.ProfileView
import nl.dpes.profilesearch.service.visitedprofiles.VisitedProfilesService
import nl.dpes.profilesearch.testcontainers.DataGenerator
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.*

import scala.collection.mutable

class GeoDistanceElasticSearchSpec(global: GlobalRead) extends BaseElasticSearch(global) {
  val lockManager: LockManager[IO] = new LockManager[IO] {
    override def runInLock[A](key: Key, fa: IO[A]): IO[A] = fa
  }

  test("It should be able to search using geo coordinates and max travel distance") { client =>
    for {
      esIndex  <- IO.randomUUID.map(_.toString)
      _        <- DataGenerator.createESIndex(client, esIndex)
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService(), lockManager))
      document <- DataGenerator.generate
      _ <- DataGenerator.insertDocument(
        client = client,
        index = esIndex,
        id = "1",
        document = documentWithGeoData(document, 55.12, 11.12, 10.0)
      )
      _ <- DataGenerator.insertDocument(
        client = client,
        index = esIndex,
        id = "2",
        document = documentWithGeoData(document, 55.12, 33.12, 10.0)
      )
      pagination <- pagination(1, 100)
      profilesWithinTheRadius <- service.getProfiles(
        pagination = pagination,
        filters = List(
          nl.dpes.profilesearch.domain.filter.GeoDistanceFilter(55.12, 11.12)
        ),
        recruiterId = None
      )
    } yield expect(profilesWithinTheRadius.totalNumberOfProfiles == 1)

  }

  private def documentWithGeoData(
    document: mutable.Map[String, Any],
    latitude: Double,
    longitude: Double,
    maxTravelDistance: Double
  ) =
    (document ++ Map(
      "commute" -> Map(
        "geoPoint"          -> Map("lat" -> latitude, "lon" -> longitude),
        "maxTravelDistance" -> maxTravelDistance
      )
    )).toMap
}
