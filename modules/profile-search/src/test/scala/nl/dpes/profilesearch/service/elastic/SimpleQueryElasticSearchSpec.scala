package nl.dpes.profilesearch.service.elastic

import cats.effect.IO
import cats.implicits.toTraverseOps
import nl.dpes.profilesearch.domain
import nl.dpes.profilesearch.domain.filter.SearchTermFilter
import nl.dpes.profilesearch.service.ProfileSearchControllerServiceSpec.{
  expect,
  pagination,
  profileSearchService,
  test,
  viewedProfilesService,
  visitedProfilesService
}
import nl.dpes.profilesearch.service.lock.{Key, LockManager}
import nl.dpes.profilesearch.service.model.profilesearch.*
import nl.dpes.profilesearch.service.model.profilesearch.sort.SortField
import nl.dpes.profilesearch.testcontainers.DataGenerator
import nl.dpes.profilesearch.testcontainers.DataGenerator.{createESIndex, insertDocument}
import weaver.*

import java.util.UUID
import scala.collection.mutable

class SimpleQueryElasticSearchSpec(global: GlobalRead) extends BaseElasticSearch(global) {
  val lockManager: LockManager[IO] = new LockManager[IO] {
    override def runInLock[A](key: Key, fa: IO[A]): IO[A] = fa
  }

  test("It should allow searching for profiles using a search term") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService(), lockManager))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- (1 to 5).toList.traverse(counter =>
        insertDocument(client, esIndex, counter.toString, (document += (SortField.updatedDate.name -> (1288566000 + counter))).toMap)
      )
      _ <- insertDocument(
        client,
        esIndex,
        "6",
        (document += (SortField.updatedDate.name -> (1288566000 + 6)) += ("preferredJobs" -> List("Scala developer"))).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <-
        service.getProfiles(
          pagination = pagination,
          filters = List(SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")))
        )
    } yield expect.same(
      List(
        profile.copy(
          id = ProfileId.unsafe("6"),
          updatedDate = UpdatedDate(1288566006),
          preferredJobs = List(PreferredJob("Scala developer"))
        )
      ),
      searchResult.profiles
    ) && expect.same(1, searchResult.totalNumberOfProfiles)
  }

  test("It should search by a job title") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService(), lockManager))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client = client,
        index = esIndex,
        id = "11",
        document = (document += (
          "experiences" -> List(
            Map(
              "experienceId" -> "ebb1aca2-1234-1234-1234-f4f260d31234",
              "companyName"  -> "ACME",
              "fromDate"     -> "2002-9",
              "toDate"       -> "2006-6",
              "jobTitle"     -> "Financial Advisor"
            )
          )
        )).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Financial Advisor"))
        )
      )
    } yield expect.same(1, searchResult.profiles.size) && expect.same(ProfileId.unsafe("11"), searchResult.profiles.head.id)
  }

  test("It should search by a function group") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService(), lockManager))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client = client,
        index = esIndex,
        id = "11",
        document = (document += (
          "functionGroups" -> List("Financial Advisor")
        )).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Financial Advisor"))
        )
      )
    } yield expect.same(1, searchResult.profiles.size) && expect.same(ProfileId.unsafe("11"), searchResult.profiles.head.id)
  }

}
