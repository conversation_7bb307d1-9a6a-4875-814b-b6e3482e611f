package nl.dpes.profilesearch.service

import cats.data.NonEmptyList
import cats.effect.{IO, IOLocal, Resource}
import cats.implicits.*
import com.sksamuel.elastic4s.ElasticClient
import com.sksamuel.elastic4s.cats.effect.instances.*
import nl.dpes.profilesearch.creditservice.CorrelationId
import nl.dpes.profilesearch.creditservice.CreditServiceClient.NotEntitled
import nl.dpes.profilesearch.service.visitedprofiles.ProfileView.{Existing, New}
import nl.dpes.profilesearch.service.visitedprofiles
import nl.dpes.profilesearch.creditservice
import nl.dpes.profilesearch.service.visitedprofiles.VisitedProfilesService.ViewsExceeded
import nl.dpes.profilesearch.service.visitedprofiles.{ProfileView, VisitedProfilesService}
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profilesearch.testcontainers.{DataGenerator, ElasticSearchContainer}
import nl.dpes.profilesearch.testcontainers.DataGenerator.*
import nl.dpes.profilesearch.testcontainers.ElasticSearchContainer.elasticClient
import nl.dpes.profilesearch.domain.filter.*
import nl.dpes.profilesearch.service.model.dbmodel.FacetsResult.TermBucket
import nl.dpes.profilesearch.domain
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.IOSuite
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.domain.Error.ProfileNotFound
import nl.dpes.profilesearch.service.lock.{Key, LockManager}
import nl.dpes.profilesearch.service.model.profilesearch.{
  AggregationKeyName,
  LastViewedDate,
  PreferredJob,
  ProfileId,
  RecruiterId,
  SearchResult,
  UpdatedDate
}
import nl.dpes.profilesearch.service.model.profilesearch.sort.*
import nl.dpes.profilesearch.service.model.profilesearch.suggestions.{CityPrefix, CitySuggestion}
import nl.dpes.profilesearch.service.viewedprofiles.ViewedProfilesService

import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.UUID
import scala.concurrent.duration.*
import scala.util.Random
import weaver.GlobalRead

// todo: Use fixed date time in the test
object ProfileSearchControllerServiceSpec extends IOSuite {

  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  override type Res = ElasticClient

  override def sharedResource: Resource[IO, ElasticClient] = elasticClient

  def lockManager = new LockManager[IO] {
    override def runInLock[A](key: Key, fa: IO[A]): IO[A] = fa
  }

  val lastViewedDate: profilesearch.LastViewedDate = profilesearch.LastViewedDate(1738845299914L)
  val profile: profilesearch.ProfileSearchResult = profilesearch.ProfileSearchResult(
    id = profilesearch.ProfileId.unsafe("7cd63c42-1234-1234-1234-19fb61915119"),
    name = Some(profilesearch.Name(profilesearch.FirstName("John"), profilesearch.LastName("Doe"))),
    updatedDate = profilesearch.UpdatedDate(1288566000),
    workingHours = Some(profilesearch.WorkingHours(36, 40)),
    workLevels = List(profilesearch.WorkLevel("HBO")),
    experiences =
      List(profilesearch.Experience("Recruiter"), profilesearch.Experience("UX Designer"), profilesearch.Experience("English teacher")),
    preferredJobs = List(profilesearch.PreferredJob("management assistant")),
    photo = Some(profilesearch.Photo("profile-photos/7cd63c42-1234-1234-1234-01fb6a415123/152dea87-1234-1234-1234-dfb501ab1234.jpg")),
    city = Some(profilesearch.City("Amsterdam")),
    lastViewedDate = None
  )

  def visitedProfilesService(
    registerFn: (profilesearch.RecruiterId, profilesearch.ProfileId) => IO[ProfileView] = (_, _) => IO(New)
  ): VisitedProfilesService[IO] =
    (recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId) => registerFn(recruiterId, profileId)

  def viewedProfilesService(
    saveFn: (profilesearch.RecruiterId, profilesearch.ProfileId) => IO[Unit] = (_, _) => IO(()),
    getLastViewedDateFn: => IO[Option[LastViewedDate]] = IO(Some(lastViewedDate))
  ): ViewedProfilesService[IO] = new ViewedProfilesService[IO] {
    override def save(recruiterId: RecruiterId, profileId: ProfileId): IO[Unit] = saveFn(recruiterId, profileId)

    override def getLastViewedDate(recruiterId: RecruiterId, profileId: ProfileId): IO[Option[LastViewedDate]] = getLastViewedDateFn
  }

  def pagination(number: Int, size: Int): IO[profilesearch.pagination.Pagination] =
    IO.fromEither(
      (profilesearch.pagination.PageNumber(number), profilesearch.pagination.PageSize(size))
        .parMapN(profilesearch.pagination.Pagination.apply)
        .leftMap(msg => new Exception(msg))
    )

  def profileSearchService(
    esIndex: String,
    client: ElasticClient,
    visitedProfilesService: VisitedProfilesService[IO],
    viewedProfilesService: ViewedProfilesService[IO],
    lockManager: LockManager[IO] = lockManager
  ): ProfileSearchService[IO] = ProfileSearchService.impl[IO](esIndex)(client, visitedProfilesService, viewedProfilesService, lockManager)

  test("It should return the profiles with aggregations in a paginated way") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service           <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _                 <- createESIndex(client, esIndex)
      initialPagination <- pagination(1, 100)
      noProfilesYet     <- service.getProfiles(pagination = initialPagination)
      document          <- DataGenerator.generate
      _                 <- (1 to 225).toList.traverse(id => insertDocument(client, esIndex, id.toString, document.toMap))
      firstPagination   <- pagination(1, 50)
      firstPage         <- service.getProfiles(pagination = firstPagination)
      secondPagination  <- pagination(2, 50)
      secondPage        <- service.getProfiles(pagination = secondPagination)
      thirdPagination   <- pagination(3, 50)
      thirdPage         <- service.getProfiles(pagination = thirdPagination)
      fourthPagination  <- pagination(4, 50)
      fourthPage        <- service.getProfiles(pagination = fourthPagination)
      fifthPagination   <- pagination(5, 50)
      fifthPage         <- service.getProfiles(pagination = fifthPagination)
      sixthPagination   <- pagination(6, 50)
      sixthPage         <- service.getProfiles(pagination = sixthPagination)
    } yield expect(
      noProfilesYet ==
        SearchResult(
          List(),
          Map(
            AggregationKeyName("provinces") -> List(),
            AggregationKeyName("updatedDate") -> List(
              TermBucket("Afgelopen 24 uur", 0),
              TermBucket("Afgelopen 3 dagen", 0),
              TermBucket("Afgelopen week", 0),
              TermBucket("Afgelopen maand", 0),
              TermBucket("Afgelopen 3 maanden", 0),
              TermBucket("Afgelopen 6 maanden", 0),
              TermBucket("Afgelopen jaar", 0),
              TermBucket("Alles", 0)
            ),
            AggregationKeyName("functionGroups")    -> List(),
            AggregationKeyName("workLevels")        -> List(),
            AggregationKeyName("workingHours")      -> List(),
            AggregationKeyName("careerLevels")      -> List(),
            AggregationKeyName("requestedSalaries") -> List(),
            AggregationKeyName("availabilities")    -> List(),
            AggregationKeyName("driverLicenses")    -> List(),
            AggregationKeyName("languages")         -> List()
          ),
          0
        ) &&
        firstPage.profiles.size + secondPage.profiles.size + thirdPage.profiles.size +
        fourthPage.profiles.size + fifthPage.profiles.size + sixthPage.profiles.size == 225 &&
        sixthPage.profiles.isEmpty &&
        firstPage.profiles.intersect(secondPage.profiles).isEmpty &&
        firstPage.profiles.intersect(thirdPage.profiles).isEmpty &&
        firstPage.profiles.intersect(fourthPage.profiles).isEmpty &&
        firstPage.profiles.intersect(fifthPage.profiles).isEmpty &&
        firstPage.aggregations(AggregationKeyName("provinces")) == List(TermBucket("Noord-Holland", 225)) &&
        firstPage.aggregations(AggregationKeyName("functionGroups")) == List(TermBucket("Administratief/Secretarieel", 225)) &&
        firstPage.aggregations(AggregationKeyName("workLevels")) == List(TermBucket("HBO", 225)) &&
        firstPage.aggregations(AggregationKeyName("workingHours")) == List(TermBucket("32 tot en met 40 uur", 225)) &&
        firstPage.aggregations(AggregationKeyName("careerLevels")) == List(TermBucket("Directie", 225)) &&
        firstPage.aggregations(AggregationKeyName("requestedSalaries")) == List(TermBucket("5.000 - 7.000", 225)) &&
        firstPage.aggregations(AggregationKeyName("availabilities")) == List(TermBucket("In overleg", 225)) &&
        firstPage.aggregations(AggregationKeyName("driverLicenses")) == List(TermBucket("B - personenauto", 225)) &&
        firstPage.aggregations(AggregationKeyName("languages")) == List(TermBucket("Engels", 225), TermBucket("Nederlands", 225)) &&
        firstPage.aggregations(AggregationKeyName("updatedDate")) == List(
          TermBucket("Afgelopen 24 uur", 0),
          TermBucket("Afgelopen 3 dagen", 0),
          TermBucket("Afgelopen week", 0),
          TermBucket("Afgelopen maand", 0),
          TermBucket("Afgelopen 3 maanden", 0),
          TermBucket("Afgelopen 6 maanden", 0),
          TermBucket("Afgelopen jaar", 0),
          TermBucket("Alles", 225)
        )
    )
  }

  test("It should allow searching for profiles using filters only") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "6",
        (document += ("commute" -> Map("province" -> "Zeeland", "city" -> "Eede")) += ("functionGroups" -> List("Medisch/Zorg"))
          += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("HAVO")
          += "workingHours"  -> List("16 tot 24 uur") += ("careerLevel" -> "Leidinggevend") += ("requestedSalary" -> "< 1.750")
          += ("availability"    -> "In overleg")
          += ("driverLicenses"  -> List(Map("type" -> "B - personenauto", "group" -> "B - personenauto")))).toMap
      )
      _ <- (1 to 5).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document +=
            ("commute" -> Map("province" -> "Noord-Holland", "city" -> "Amsterdam")) += ("functionGroups" -> List("Techniek"))
            += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("HBO") += "workingHours" -> List("24 tot 32 uur")
            += ("careerLevel" -> "Starter") += ("requestedSalary" -> "5.000 - 7.000") += ("availability" -> "Per direct")
            += ("driverLicenses" -> List(
              Map("type" -> "B - personenauto", "group" -> "B - personenauto"),
              Map("type" -> "A - motor", "group"        -> "A - motor")
            ))).toMap
        )
      )
      pagination <- pagination(1, 100)
      searchResult <-
        service.getProfiles(
          pagination = pagination,
          filters = List(
            FunctionGroupFilter(NonEmptyList.of(domain.FunctionGroup("Techniek"))),
            WorkLevelFilter(NonEmptyList.of(domain.WorkLevel("HBO"), domain.WorkLevel("HAVO"))),
            WorkingHourFilter(NonEmptyList.of(domain.WorkingHours.From24To32, domain.WorkingHours.From16To24)),
            AvailabilityFilter(NonEmptyList.of(domain.Availability.PerDirect, domain.Availability.InOverleg))
          )
        )
    } yield expect(searchResult.profiles.size == 5)
  }

  test("It should be able to filter profiles using the location") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- (1 to 5).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("commute" -> Map("city" -> "Den Haag")) += "preferredJobs" -> List("Scala developer")).toMap
        )
      )
      _ <- (6 to 9).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("commute" -> Map("city" -> "Amsterdam")) += "preferredJobs" -> List("Typescript developer")).toMap
        )
      )
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("commute" -> Map("city" -> "Rotterdam")) += "preferredJobs" -> List("Recruiter")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("commute" -> Map("city" -> "Amsterdam")) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("commute" -> Map("city" -> "Utrecht")) += "preferredJobs" -> List("Recruiter")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")), CityFilter(domain.City("Amsterdam")))
      )
    } yield expect(searchResult.profiles.size == 1)
  }

  test("It should add provinces aggregation to the search result") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- (1 to 5).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("commute" -> Map("province" -> "Noord-Holland")) += "preferredJobs" -> List("Scala developer")).toMap
        )
      )
      _ <- (6 to 9).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("commute" -> Map("province" -> "Zeeland")) += "preferredJobs" -> List("Scala developer")).toMap
        )
      )
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("commute" -> Map("province" -> "Gelderland")) += "preferredJobs" -> List("Recruiter")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("commute" -> Map("province" -> "Utrecht")) += "preferredJobs" -> List("Recruiter")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("commute" -> Map("province" -> "Groningen")) += "preferredJobs" -> List("Recruiter")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")),
          ProvinceFilter(NonEmptyList.of(domain.Province("Noord-Holland"), domain.Province("Zeeland")))
        )
      )
    } yield expect(
      searchResult.profiles.size == 9 &&
        searchResult.aggregations(AggregationKeyName("provinces")) == List(
          TermBucket("Gelderland", 0),
          TermBucket("Groningen", 0),
          TermBucket("Noord-Holland", 5),
          TermBucket("Utrecht", 0),
          TermBucket("Zeeland", 4)
        )
    )
  }

  test("It should add function_groups aggregation to the search result") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- (1 to 5).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("functionGroups" -> List("Techniek")) += "preferredJobs" -> List("Scala developer")).toMap
        )
      )
      _ <- (6 to 9).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("functionGroups" -> List("Medisch/Zorg")) += "preferredJobs" -> List("Scala developer")).toMap
        )
      )
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("functionGroups" -> List("Directie/Management algemeen")) += "preferredJobs" -> List("Recruiter")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("functionGroups" -> List("Commercieel/Verkoop")) += "preferredJobs" -> List("Recruiter")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("functionGroups" -> List("Financieel/Accounting")) += "preferredJobs" -> List("Recruiter")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")),
          FunctionGroupFilter(NonEmptyList.of(domain.FunctionGroup("Techniek"), domain.FunctionGroup("Directie/Management algemeen")))
        )
      )
    } yield expect(
      searchResult.profiles.size == 5 &&
        searchResult.aggregations(AggregationKeyName("functionGroups")) == List(
          TermBucket("Commercieel/Verkoop", 0),
          TermBucket("Directie/Management algemeen", 0),
          TermBucket("Financieel/Accounting", 0),
          TermBucket("Medisch/Zorg", 4),
          TermBucket("Techniek", 5)
        )
    )
  }

  test("It should use 'AND' operator for 'provinces' and 'function_groups' filters when looking for a search term") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- (1 to 5).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document +=
            ("commute" -> Map("province" -> "Noord-Holland")) += ("functionGroups" -> List("Techniek"))
            += "preferredJobs" -> List("Scala developer")).toMap
        )
      )
      _ <- (6 to 9).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("commute" -> Map("province" -> "Zeeland")) += ("functionGroups" -> List("Medisch/Zorg"))
            += "preferredJobs" -> List("Scala developer")).toMap
        )
      )
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("commute" -> Map("province" -> "Gelderland")) += ("functionGroups" -> List("Directie/Management algemeen"))
          += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("commute" -> Map("province" -> "Noord-Holland")) += ("functionGroups" -> List("Commercieel/Verkoop"))
          += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("commute" -> Map("province" -> "Groningen")) += ("functionGroups" -> List("Techniek"))
          += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("commute" -> Map("province" -> "Zeeland")) += ("functionGroups" -> List("Directie/Management algemeen"))
          += "preferredJobs" -> List("Scala developer")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")),
          ProvinceFilter(NonEmptyList.of(domain.Province("Noord-Holland"), domain.Province("Zeeland"))),
          FunctionGroupFilter(NonEmptyList.of(domain.FunctionGroup("Techniek"), domain.FunctionGroup("Directie/Management algemeen")))
        )
      )
    } yield expect(
      searchResult.profiles.size == 6 &&
        searchResult.aggregations(AggregationKeyName("provinces")) == List(
          TermBucket("Gelderland", 1),
          TermBucket("Groningen", 1),
          TermBucket("Noord-Holland", 5),
          TermBucket("Zeeland", 1)
        ) &&
        searchResult.aggregations(AggregationKeyName("functionGroups")) == List(
          TermBucket("Commercieel/Verkoop", 1),
          TermBucket("Directie/Management algemeen", 1),
          TermBucket("Medisch/Zorg", 4),
          TermBucket("Techniek", 5)
        )
    )
  }

  test("It should be able to return profiles when searching with partial and case-insensitive search terms") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service    <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _          <- createESIndex(client, esIndex)
      document   <- DataGenerator.generate
      _          <- insertDocument(client, esIndex, "10", (document += "preferredJobs" -> List("Scala Lead")).toMap)
      _          <- insertDocument(client, esIndex, "11", (document += "preferredJobs" -> List("Scala developer")).toMap)
      _          <- insertDocument(client, esIndex, "12", (document += "preferredJobs" -> List("Java developer")).toMap)
      _          <- insertDocument(client, esIndex, "13", (document += "preferredJobs" -> List("Scala Architect")).toMap)
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala"))
        )
      )
    } yield expect(searchResult.profiles.size == 3)
  }

  test("It should add the workLevels aggregation to the search result") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- (1 to 5).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("workLevels" -> List("HBO")) += "preferredJobs" -> List("Scala developer")).toMap
        )
      )
      _ <- (6 to 9).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("workLevels" -> List("HAVO")) += "preferredJobs" -> List("Typescript developer")).toMap
        )
      )
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("workLevels" -> List("MBO")) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("workLevels" -> List("HBO")) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("workLevels" -> List("HAVO")) += "preferredJobs" -> List("Java developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("workLevels" -> List("MBO")) += "preferredJobs" -> List("Scala developer")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")),
          WorkLevelFilter(NonEmptyList.of(domain.WorkLevel("MBO"), domain.WorkLevel("HAVO")))
        )
      )
    } yield expect(
      searchResult.profiles.size == 2 &&
        searchResult.aggregations(AggregationKeyName("workLevels")) == List(
          TermBucket("HBO", 6),
          TermBucket("MBO", 2),
          TermBucket("HAVO", 0)
        )
    )
  }

  test("It should add the workingHours aggregation to the search result") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("workingHours" -> List("16 tot 24 uur")) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("workingHours" -> List("24 tot 32 uur")) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("workingHours" -> List("16 tot 24 uur")) += "preferredJobs" -> List("Java developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("workingHours" -> List("Tot 16 uur", "24 tot 32 uur")) += "preferredJobs" -> List("Scala developer")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")),
          WorkingHourFilter(NonEmptyList.of(domain.WorkingHours.UpTo16Hours, domain.WorkingHours.From24To32))
        )
      )
    } yield expect(
      searchResult.profiles.size == 2 &&
        searchResult.aggregations(AggregationKeyName("workingHours")) == List(
          TermBucket("Tot 16 uur", 1),
          TermBucket("16 tot 24 uur", 1),
          TermBucket("24 tot 32 uur", 2)
        )
    )
  }

  test("It should add the careerLevels aggregation to the search result") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("careerLevel" -> "Directie") += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("careerLevel" -> "Starter") += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("careerLevel" -> "Directie") += "preferredJobs" -> List("Java developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("careerLevel" -> "Leidinggevend") += "preferredJobs" -> List("Scala developer")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          CareerLevelFilter(NonEmptyList.of(domain.CareerLevel("Directie"), domain.CareerLevel("Leidinggevend")))
        )
      )
    } yield expect(searchResult.profiles.size == 3) && expect(
      searchResult.aggregations(AggregationKeyName("careerLevels")) == List(
        TermBucket("Starter", 1),
        TermBucket("Leidinggevend", 1),
        TermBucket("Directie", 2)
      )
    )
  }

  test("It should add the requestedSalary aggregation to the search result") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("requestedSalary" -> "1.750 - 2.500") += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("requestedSalary" -> "5.000 - 7.000") += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("requestedSalary" -> "1.750 - 2.500") += "preferredJobs" -> List("Java developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("requestedSalary" -> "< 1.750") += "preferredJobs" -> List("Scala developer")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")),
          RequestedSalaryFilter(NonEmptyList.of(domain.RequestedSalary.From1750To2500, domain.RequestedSalary.UpTo1750))
        )
      )
    } yield expect(
      searchResult.profiles.size == 2 &&
        searchResult.aggregations(AggregationKeyName("requestedSalaries")) == List(
          TermBucket("< 1.750", 1),
          TermBucket("1.750 - 2.500", 1),
          TermBucket("5.000 - 7.000", 1)
        )
    )
  }

  test("It should add the availability aggregation to the search result") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("availability" -> "Per direct") += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("availability" -> "In overleg") += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("availability" -> "Per direct") += "preferredJobs" -> List("Java developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("availability" -> "In overleg") += "preferredJobs" -> List("Scala developer")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          AvailabilityFilter(NonEmptyList.of(domain.Availability.PerDirect, domain.Availability.InOverleg)),
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer"))
        )
      )
    } yield expect(
      searchResult.profiles.size == 3 &&
        searchResult.aggregations(AggregationKeyName("availabilities")).sortBy(_.key) == List(
          TermBucket("In overleg", 2),
          TermBucket("Per direct", 1)
        )
    )
  }

  test("It should add the driver licenses aggregation to the search result") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("driverLicenses" -> List(
          Map("type" -> "B - personenauto", "group" -> "B - personenauto"),
          Map("type" -> "A - motor", "group"        -> "A - motor")
        )) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("driverLicenses" -> List(
          Map("type" -> "C - vrachtwagen", "group" -> "C - vrachtwagen"),
          Map("type" -> "A - motor", "group"       -> "A - motor")
        )) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("driverLicenses" -> List(
          Map("type" -> "B - personenauto", "group" -> "B - personenauto")
        )) += "preferredJobs" -> List("Java developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("driverLicenses" -> List(
          Map("type" -> "B - personenauto", "group"    -> "B - personenauto"),
          Map("type" -> "A - motor", "group"           -> "A - motor"),
          Map("type" -> "D - bus >8 personen", "group" -> "D - bus >8 personen")
        )) += "preferredJobs" -> List("Scala developer")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          DriversLicenseFilter(
            NonEmptyList.of(
              domain.DriversLicense("B - personenauto"),
              domain.DriversLicense("D - bus >8 personen")
            )
          )
        )
      )
    } yield expect(
      searchResult.profiles.size == 3 &&
        searchResult.aggregations(AggregationKeyName("driverLicenses")) == List(
          TermBucket("A - motor", 3),
          TermBucket("B - personenauto", 3),
          TermBucket("C - vrachtwagen", 1),
          TermBucket("D - bus >8 personen", 1)
        )
    )
  }

  test("It should add the languages aggregation to the search result") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("languages" -> List(
          Map("isoCode" -> "EN", "name" -> "Engles", "isNative"    -> false),
          Map("isoCode" -> "PR", "name" -> "Portugees", "isNative" -> false)
        )) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("languages" -> List(
          Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"   -> false),
          Map("isoCode" -> "NL", "name" -> "Nederlands", "isNative" -> false)
        )) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("languages" -> List(
          Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"   -> false),
          Map("isoCode" -> "NL", "name" -> "Nederlands", "isNative" -> false),
          Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"     -> false)
        )) += "preferredJobs" -> List("Java developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("languages" -> List(
          Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"  -> false),
          Map("isoCode" -> "KN", "name" -> "Kantonees", "isNative" -> false),
          Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"    -> false)
        )) += "preferredJobs" -> List("Scala developer")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")),
          LanguageFilter(NonEmptyList.of(domain.Language("Arabisch"), domain.Language("Kantonees")))
        )
      )
    } yield expect(
      searchResult.profiles.size == 2 &&
        searchResult.aggregations(AggregationKeyName("languages")).sortBy(_.key) == List(
          TermBucket("Arabisch", 2),
          TermBucket("Engles", 1),
          TermBucket("Kantonees", 1),
          TermBucket("Nederlands", 1),
          TermBucket("Portugees", 1),
          TermBucket("Spaans", 1)
        )
    )
  }

  test("It should add the updatedDate aggregation to the search result") { client =>
    val esIndex = UUID.randomUUID().toString
    val extraTime = 100
    val lastYear = ZonedDateTime.now().minus(1, ChronoUnit.YEARS).toEpochSecond + extraTime
    val last6Months = ZonedDateTime.now().minus(6, ChronoUnit.MONTHS).toEpochSecond + extraTime
    val last3Months = ZonedDateTime.now().minus(3, ChronoUnit.MONTHS).toEpochSecond + extraTime
    val last24Hours = ZonedDateTime.now().minus(1, ChronoUnit.DAYS).toEpochSecond + extraTime

    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("updatedDate" -> last6Months) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("updatedDate" -> (lastYear + 100000)) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("updatedDate" -> last6Months) += "preferredJobs" -> List("Java developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("updatedDate" -> (last3Months + 100000)) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "14",
        (document += ("updatedDate" -> (last24Hours + 10)) += "preferredJobs" -> List("Scala developer")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")),
          UpdateDateFilter(domain.UpdateDate.Last3Months)
        )
      )
    } yield expect(
      searchResult.profiles.size == 2 &&
        searchResult.aggregations(AggregationKeyName("updatedDate")) == List(
          TermBucket("Afgelopen 24 uur", 1),
          TermBucket("Afgelopen 3 dagen", 1),
          TermBucket("Afgelopen week", 1),
          TermBucket("Afgelopen maand", 1),
          TermBucket("Afgelopen 3 maanden", 2),
          TermBucket("Afgelopen 6 maanden", 3),
          TermBucket("Afgelopen jaar", 4),
          TermBucket("Alles", 4)
        )
    )
  }

  test("It should exclude non-findable profiles from filters and aggregations") { client =>
    val esIndex = UUID.randomUUID().toString

    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("findable" -> true) += "preferredJobs" -> List("Scala developer") += ("functionGroups" -> List("Techniek"))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("findable" -> true) += "preferredJobs" -> List("Scala developer") += ("functionGroups" -> List(
          "Consultancy/Advies"
        ))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("findable" -> true) += "preferredJobs" -> List("Java developer") +=
          ("functionGroups"      -> List("Automatisering/Internet"))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("findable" -> false) += "preferredJobs" -> List("Scala developer") +=
          ("functionGroups"      -> List("Administratief/Secretarieel"))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "14",
        (document += ("findable" -> false) += "preferredJobs" -> List("Scala developer") += ("functionGroups" -> List("Overig"))).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination
      )
    } yield expect(
      searchResult.profiles.size == 3
    )
      && expect(
        searchResult.aggregations(AggregationKeyName("functionGroups")) == List(
          TermBucket("Automatisering/Internet", 1),
          TermBucket("Consultancy/Advies", 1),
          TermBucket("Techniek", 1)
        )
      )
  }

  test("It should sort function_groups alphabetically") { client =>
    val esIndex = UUID.randomUUID().toString

    for {
      service      <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _            <- createESIndex(client, esIndex)
      document     <- DataGenerator.generate
      _            <- insertDocument(client, esIndex, "10", (document += ("functionGroups" -> List("Techniek"))).toMap)
      _            <- insertDocument(client, esIndex, "12", (document += ("functionGroups" -> List("Automatisering/Internet"))).toMap)
      _            <- insertDocument(client, esIndex, "13", (document += ("functionGroups" -> List("Administratief/Secretarieel"))).toMap)
      _            <- insertDocument(client, esIndex, "14", (document += ("functionGroups" -> List("Overig"))).toMap)
      pagination   <- pagination(1, 100)
      searchResult <- service.getProfiles(pagination = pagination)
    } yield expect(
      searchResult.profiles.size == 4 &&
        searchResult.aggregations(AggregationKeyName("functionGroups")) == List(
          TermBucket("Administratief/Secretarieel", 1),
          TermBucket("Automatisering/Internet", 1),
          TermBucket("Overig", 1),
          TermBucket("Techniek", 1)
        )
    )
  }

  test("It should sort the requested salaries from lowest to highest salary range") { client =>
    val esIndex = UUID.randomUUID().toString

    for {
      service      <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _            <- createESIndex(client, esIndex)
      document     <- DataGenerator.generate
      _            <- insertDocument(client, esIndex, "10", (document += ("requestedSalary" -> "1.750 - 2.500")).toMap)
      _            <- insertDocument(client, esIndex, "11", (document += ("requestedSalary" -> "3.500 - 5.000")).toMap)
      _            <- insertDocument(client, esIndex, "12", (document += ("requestedSalary" -> "> 7.000")).toMap)
      _            <- insertDocument(client, esIndex, "13", (document += ("requestedSalary" -> "5.000 - 7.000")).toMap)
      _            <- insertDocument(client, esIndex, "14", (document += ("requestedSalary" -> "2.500 - 3.500")).toMap)
      _            <- insertDocument(client, esIndex, "17", (document += ("requestedSalary" -> "< 1.750")).toMap)
      pagination   <- pagination(1, 100)
      searchResult <- service.getProfiles(pagination = pagination)
    } yield expect(
      searchResult.profiles.size == 6 &&
        searchResult.aggregations(AggregationKeyName("requestedSalaries")) == List(
          TermBucket("< 1.750", 1),
          TermBucket("1.750 - 2.500", 1),
          TermBucket("2.500 - 3.500", 1),
          TermBucket("3.500 - 5.000", 1),
          TermBucket("5.000 - 7.000", 1),
          TermBucket("> 7.000", 1)
        )
    )
  }

  test("It should sort the working hours from lowest to highest range") { client =>
    val esIndex = UUID.randomUUID().toString

    for {
      service      <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _            <- createESIndex(client, esIndex)
      document     <- DataGenerator.generate
      _            <- insertDocument(client, esIndex, "10", (document += ("workingHours" -> "16 tot 24 uur")).toMap)
      _            <- insertDocument(client, esIndex, "11", (document += ("workingHours" -> "Tot 16 uur")).toMap)
      _            <- insertDocument(client, esIndex, "12", (document += ("workingHours" -> "32 tot en met 40 uur")).toMap)
      _            <- insertDocument(client, esIndex, "15", (document += ("workingHours" -> "24 tot 32 uur")).toMap)
      pagination   <- pagination(1, 100)
      searchResult <- service.getProfiles(pagination = pagination)
    } yield expect(
      searchResult.profiles.size == 4 &&
        searchResult.aggregations(AggregationKeyName("workingHours")) == List(
          TermBucket("Tot 16 uur", 1),
          TermBucket("16 tot 24 uur", 1),
          TermBucket("24 tot 32 uur", 1),
          TermBucket("32 tot en met 40 uur", 1)
        )
    )
  }

  test("It should sort the career levels from lowest to highest level") { client =>
    val esIndex = UUID.randomUUID().toString

    for {
      service      <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _            <- createESIndex(client, esIndex)
      document     <- DataGenerator.generate
      _            <- insertDocument(client, esIndex, "10", (document += ("careerLevel" -> "Ervaren")).toMap)
      _            <- insertDocument(client, esIndex, "11", (document += ("careerLevel" -> "Starter")).toMap)
      _            <- insertDocument(client, esIndex, "12", (document += ("careerLevel" -> "Senior management")).toMap)
      _            <- insertDocument(client, esIndex, "13", (document += ("careerLevel" -> "Leidinggevend")).toMap)
      _            <- insertDocument(client, esIndex, "14", (document += ("careerLevel" -> "Directie")).toMap)
      pagination   <- pagination(1, 100)
      searchResult <- service.getProfiles(pagination = pagination)
    } yield expect(
      searchResult.profiles.size == 5 &&
        searchResult.aggregations(AggregationKeyName("careerLevels")) == List(
          TermBucket("Starter", 1),
          TermBucket("Ervaren", 1),
          TermBucket("Leidinggevend", 1),
          TermBucket("Senior management", 1),
          TermBucket("Directie", 1)
        )
    )
  }

  test("It should sort the work levels from lowest to highest level") { client =>
    val esIndex = UUID.randomUUID().toString

    for {
      service      <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _            <- createESIndex(client, esIndex)
      document     <- DataGenerator.generate
      _            <- insertDocument(client, esIndex, "10", (document += ("workLevels" -> List("VMBO/Mavo"))).toMap)
      _            <- insertDocument(client, esIndex, "11", (document += ("workLevels" -> List("Lagere school"))).toMap)
      _            <- insertDocument(client, esIndex, "12", (document += ("workLevels" -> List("VWO"))).toMap)
      _            <- insertDocument(client, esIndex, "13", (document += ("workLevels" -> List("HAVO"))).toMap)
      _            <- insertDocument(client, esIndex, "14", (document += ("workLevels" -> List("MBO"))).toMap)
      _            <- insertDocument(client, esIndex, "15", (document += ("workLevels" -> List("LBO"))).toMap)
      _            <- insertDocument(client, esIndex, "18", (document += ("workLevels" -> List("WO"))).toMap)
      _            <- insertDocument(client, esIndex, "19", (document += ("workLevels" -> List("Postdoctoraal"))).toMap)
      _            <- insertDocument(client, esIndex, "20", (document += ("workLevels" -> List("HBO"))).toMap)
      pagination   <- pagination(1, 100)
      searchResult <- service.getProfiles(pagination = pagination)
      workLevels = searchResult.aggregations(AggregationKeyName("workLevels"))
    } yield expect(
      searchResult.profiles.size == 9 &&
        workLevels == List(
          TermBucket("Postdoctoraal", 1),
          TermBucket("WO", 1),
          TermBucket("HBO", 1),
          TermBucket("MBO", 1),
          TermBucket("VWO", 1),
          TermBucket("HAVO", 1),
          TermBucket("VMBO/Mavo", 1),
          TermBucket("LBO", 1),
          TermBucket("Lagere school", 1)
        )
    )
  }

  test("It should sort the driver licenses from lowest to highest license") { client =>
    val esIndex = UUID.randomUUID().toString

    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("driverLicenses" -> List(Map("type" -> "B - personenauto", "group" -> "B - personenauto")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("driverLicenses" -> List(Map("type" -> "A - motor", "group" -> "A - motor")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("driverLicenses" -> List(Map("type" -> "D - bus >8 personen", "group" -> "D - bus >8 personen")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("driverLicenses" -> List(Map("type" -> "T - trekkerrijbewijs", "group" -> "T - trekkerrijbewijs")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "16",
        (document += ("driverLicenses" -> List(Map("type" -> "C - vrachtwagen", "group" -> "C - vrachtwagen")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "17",
        (document += ("driverLicenses" -> List(Map("type" -> "AM - Bromfiets", "group" -> "AM - Bromfiets")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "18",
        (document += ("driverLicenses" -> List(
          Map("type" -> "BE - personenauto met aanhangwagen", "group" -> "BE - personenauto met aanhangwagen")
        ))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "19",
        (document += ("driverLicenses" -> List(
          Map("type" -> "C - Vrachtwagen met aanhanger", "group" -> "C - Vrachtwagen met aanhanger")
        ))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "20",
        (document += ("driverLicenses" -> List(Map("type" -> "C - Lichte vrachtwagen", "group" -> "C - Lichte vrachtwagen")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "21",
        (document += ("driverLicenses" -> List(
          Map("type" -> "C - Lichte vrachtwagen met aanhanger", "group" -> "C - Lichte vrachtwagen met aanhanger")
        ))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "22",
        (document += ("driverLicenses" -> List(
          Map("type" -> "D - Lichte autobus met aanhanger", "group" -> "D - Lichte autobus met aanhanger")
        ))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "23",
        (document += ("driverLicenses" -> List(Map("type" -> "D - Lichte autobus", "group" -> "D - Lichte autobus")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "24",
        (document += ("driverLicenses" -> List(Map("type" -> "D - Autobus met aanhanger", "group" -> "D - Autobus met aanhanger")))).toMap
      )
      pagination   <- pagination(1, 100)
      searchResult <- service.getProfiles(pagination = pagination)
    } yield expect(
      searchResult.profiles.size == 13 &&
        searchResult.aggregations(AggregationKeyName("driverLicenses")) == List(
          TermBucket("A - motor", 1),
          TermBucket("AM - Bromfiets", 1),
          TermBucket("B - personenauto", 1),
          TermBucket("BE - personenauto met aanhangwagen", 1),
          TermBucket("C - Lichte vrachtwagen", 1),
          TermBucket("C - Lichte vrachtwagen met aanhanger", 1),
          TermBucket("C - Vrachtwagen met aanhanger", 1),
          TermBucket("C - vrachtwagen", 1),
          TermBucket("D - Autobus met aanhanger", 1),
          TermBucket("D - Lichte autobus", 1),
          TermBucket("D - Lichte autobus met aanhanger", 1),
          TermBucket("D - bus >8 personen", 1),
          TermBucket("T - trekkerrijbewijs", 1)
        )
    )
  }

  test("It should use 'AND' operator for more than 2 filters when looking for a search term") { client =>
    val esIndex = UUID.randomUUID().toString
    val extraTime = 100
    val lastYear = ZonedDateTime.now().minus(1, ChronoUnit.YEARS).toEpochSecond + extraTime
    val last6Months = ZonedDateTime.now().minus(6, ChronoUnit.MONTHS).toEpochSecond + extraTime
    val last3Months = ZonedDateTime.now().minus(3, ChronoUnit.MONTHS).toEpochSecond + extraTime
    val last24Hours = ZonedDateTime.now().minus(1, ChronoUnit.DAYS).toEpochSecond + extraTime
    val last3Days = ZonedDateTime.now().minus(3, ChronoUnit.DAYS).toEpochSecond + extraTime
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- (1 to 5).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document +=
            ("commute" -> Map("province" -> "Noord-Holland", "city" -> "Amsterdam")) += ("functionGroups" -> List("Techniek"))
            += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("HBO") += "workingHours" -> List("24 tot 32 uur")
            += ("careerLevel" -> "Starter") += ("requestedSalary" -> "5.000 - 7.000") += ("availability" -> "Per direct")
            += ("updatedDate" -> (lastYear + 10000))
            += ("languages" -> List(
              Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"  -> false),
              Map("isoCode" -> "KN", "name" -> "Kantonees", "isNative" -> false),
              Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"    -> false)
            ))
            += ("driverLicenses" -> List(
              Map("type" -> "B - personenauto", "group" -> "B - personenauto"),
              Map("type" -> "A - motor", "group"        -> "A - motor")
            ))).toMap
        )
      )
      _ <- (6 to 9).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("commute" -> Map("province" -> "Zeeland", "city" -> "Eede")) += ("functionGroups" -> List("Medisch/Zorg"))
            += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("HAVO")
            += "workingHours"  -> List("16 tot 24 uur") += ("careerLevel" -> "Leidinggevend") += ("requestedSalary" -> "< 1.750")
            += ("languages" -> List(
              Map("isoCode" -> "EN", "name" -> "Engels", "isNative"     -> false),
              Map("isoCode" -> "NL", "name" -> "Nederlands", "isNative" -> false),
              Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"     -> false)
            ))
            += ("availability"   -> "In overleg") += ("updatedDate" -> (last3Months + 100000))
            += ("driverLicenses" -> List(Map("type" -> "B - personenauto", "group" -> "B - personenauto")))).toMap
        )
      )
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("commute" -> Map("province" -> "Gelderland", "city" -> "Achterveld"))
          += ("functionGroups"  -> List("Directie/Management algemeen"))
          += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("HBO") += "workingHours" -> List("24 tot 32 uur")
          += ("careerLevel"     -> "Starter") += ("requestedSalary" -> "3.500 - 5.000")
          += ("availability"    -> "Per direct") += ("updatedDate"  -> (last24Hours + 100))
          += ("languages" -> List(
            Map("isoCode" -> "PR", "name" -> "Portugees", "isNative" -> false),
            Map("isoCode" -> "KN", "name" -> "Kantonees", "isNative" -> false),
            Map("isoCode" -> "EN", "name" -> "Engels", "isNative"    -> false)
          ))
          += ("driverLicenses" -> List(Map("type" -> "A - motor", "group" -> "A - motor")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("commute" -> Map("province" -> "Noord-Holland", "city" -> "Amsterdam"))
          += ("functionGroups"  -> List("Commercieel/Verkoop"))
          += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("MBO") += "workingHours" -> List("16 tot 24 uur")
          += ("careerLevel"     -> "Directie") += ("requestedSalary" -> "3.500 - 5.000")
          += ("availability"    -> "In overleg") += ("updatedDate"   -> (lastYear + 10000))
          += ("languages" -> List(
            Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"   -> false),
            Map("isoCode" -> "NL", "name" -> "Nederlands", "isNative" -> false),
            Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"     -> false)
          ))
          += ("driverLicenses" -> List(
            Map("type" -> "D - bus >8 personen", "group" -> "D - bus >8 personen"),
            Map("type" -> "C - vrachtwagen", "group"     -> "C - vrachtwagen")
          ))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("commute" -> Map("province" -> "Groningen", "city" -> "Alteveer")) += ("functionGroups" -> List("Techniek"))
          += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("HBO") += "workingHours" -> List("16 tot 24 uur")
          += ("careerLevel"     -> "Leidinggevend") += ("requestedSalary"                                     -> "5.000 - 7.000")
          += ("availability"    -> "Per direct") += ("updatedDate"                                            -> (last6Months + 100000))
          += ("languages" -> List(
            Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"  -> false),
            Map("isoCode" -> "PR", "name" -> "Portugees", "isNative" -> false),
            Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"    -> false)
          ))
          += ("driverLicenses" -> List(Map("type" -> "D - bus >8 personen", "group" -> "D - bus >8 personen")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("commute" -> Map("province" -> "Zeeland", "city" -> "Eede"))
          += ("functionGroups"  -> List("Directie/Management algemeen"))
          += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("MBO")
          += "workingHours"  -> List("Tot 16 uur", "16 tot 24 uur")
          += ("careerLevel"     -> "Starter") += ("requestedSalary" -> "5.000 - 7.000")
          += ("availability"    -> "In overleg") += ("updatedDate"  -> (last3Days + 100))
          += ("languages" -> List(
            Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"  -> false),
            Map("isoCode" -> "KN", "name" -> "Kantonees", "isNative" -> false),
            Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"    -> false)
          ))
          += ("driverLicenses" -> List(Map("type" -> "B - personenauto", "group" -> "B - personenauto")))).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")),
          CityFilter(domain.City("Eede")),
          ProvinceFilter(NonEmptyList.of(domain.Province("Noord-Holland"), domain.Province("Zeeland"))),
          UpdateDateFilter(domain.UpdateDate.Last6Months),
          FunctionGroupFilter(
            NonEmptyList.of(
              domain.FunctionGroup("Techniek"),
              domain.FunctionGroup("Directie/Management algemeen")
            )
          ),
          WorkLevelFilter(NonEmptyList.of(domain.WorkLevel("MBO"), domain.WorkLevel("HAVO"))),
          WorkingHourFilter(NonEmptyList.of(domain.WorkingHours.From16To24, domain.WorkingHours.From24To32)),
          CareerLevelFilter(NonEmptyList.of(domain.CareerLevel("Starter"), domain.CareerLevel("Directie"))),
          RequestedSalaryFilter(
            NonEmptyList.of(
              domain.RequestedSalary.From5000To7000,
              domain.RequestedSalary.From3500To5000
            )
          ),
          AvailabilityFilter(
            NonEmptyList.of(
              domain.Availability.InOverleg,
              domain.Availability.PerDirect
            )
          ),
          DriversLicenseFilter(
            NonEmptyList.of(
              domain.DriversLicense("B - personenauto"),
              domain.DriversLicense("D - bus >8 personen")
            )
          ),
          LanguageFilter(
            NonEmptyList.of(
              domain.Language("Arabisch"),
              domain.Language("Kantonees")
            )
          )
        )
      )
    } yield expect(
      searchResult.profiles.size == 1 &&
        searchResult.aggregations(AggregationKeyName("provinces")) == List(
          TermBucket("Gelderland", 0),
          TermBucket("Groningen", 0),
          TermBucket("Noord-Holland", 0),
          TermBucket("Zeeland", 1)
        ) &&
        searchResult.aggregations(AggregationKeyName("functionGroups")) == List(
          TermBucket("Commercieel/Verkoop", 0),
          TermBucket("Directie/Management algemeen", 1),
          TermBucket("Medisch/Zorg", 0),
          TermBucket("Techniek", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("workLevels")) == List(
          TermBucket("HBO", 0),
          TermBucket("MBO", 1),
          TermBucket("HAVO", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("workingHours")) == List(
          TermBucket("Tot 16 uur", 1),
          TermBucket("16 tot 24 uur", 1),
          TermBucket("24 tot 32 uur", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("careerLevels")) == List(
          TermBucket("Starter", 1),
          TermBucket("Leidinggevend", 0),
          TermBucket("Directie", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("requestedSalaries")) == List(
          TermBucket("< 1.750", 0),
          TermBucket("3.500 - 5.000", 0),
          TermBucket("5.000 - 7.000", 1)
        ) &&
        searchResult.aggregations(AggregationKeyName("availabilities")).sortBy(_.key) == List(
          TermBucket("In overleg", 1),
          TermBucket("Per direct", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("driverLicenses")) == List(
          TermBucket("A - motor", 0),
          TermBucket("B - personenauto", 1),
          TermBucket("C - vrachtwagen", 0),
          TermBucket("D - bus >8 personen", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("languages")).toSet == Set(
          TermBucket("Arabisch", 1),
          TermBucket("Engels", 0),
          TermBucket("Kantonees", 1),
          TermBucket("Nederlands", 0),
          TermBucket("Portugees", 0),
          TermBucket("Spaans", 1)
        ) &&
        searchResult.aggregations(AggregationKeyName("updatedDate")).sortBy(_.key) == List(
          TermBucket("Afgelopen 24 uur", 0),
          TermBucket("Afgelopen 3 dagen", 1),
          TermBucket("Afgelopen 3 maanden", 1),
          TermBucket("Afgelopen 6 maanden", 1),
          TermBucket("Afgelopen jaar", 1),
          TermBucket("Afgelopen maand", 1),
          TermBucket("Afgelopen week", 1),
          TermBucket("Alles", 1)
        )
    )
  }

  test("It should use 'AND' operator for more than 2 filters when looking for a search term") { client =>
    val esIndex = UUID.randomUUID().toString
    val extraTime = 100
    val lastYear = ZonedDateTime.now().minus(1, ChronoUnit.YEARS).toEpochSecond + extraTime
    val last6Months = ZonedDateTime.now().minus(6, ChronoUnit.MONTHS).toEpochSecond + extraTime
    val last3Months = ZonedDateTime.now().minus(3, ChronoUnit.MONTHS).toEpochSecond + extraTime
    val last24Hours = ZonedDateTime.now().minus(1, ChronoUnit.DAYS).toEpochSecond + extraTime
    val last3Days = ZonedDateTime.now().minus(3, ChronoUnit.DAYS).toEpochSecond + extraTime
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- (1 to 5).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document +=
            ("commute" -> Map("province" -> "Noord-Holland", "city" -> "Amsterdam")) += ("functionGroups" -> List("Techniek"))
            += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("HBO") += "workingHours" -> List("24 tot 32 uur")
            += ("careerLevel" -> "Starter") += ("requestedSalary" -> "5.000 - 7.000") += ("availability" -> "Per direct")
            += ("updatedDate" -> (lastYear + 10000))
            += ("languages" -> List(
              Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"  -> false),
              Map("isoCode" -> "KN", "name" -> "Kantonees", "isNative" -> false),
              Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"    -> false)
            ))
            += ("driverLicenses" -> List(
              Map("type" -> "B - personenauto", "group" -> "B - personenauto"),
              Map("type" -> "A - motor", "group"        -> "A - motor")
            ))).toMap
        )
      )
      _ <- (6 to 9).toList.traverse(counter =>
        insertDocument(
          client,
          esIndex,
          counter.toString,
          (document += ("commute" -> Map("province" -> "Zeeland", "city" -> "Eede")) += ("functionGroups" -> List("Medisch/Zorg"))
            += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("HAVO")
            += "workingHours"  -> List("16 tot 24 uur") += ("careerLevel" -> "Leidinggevend") += ("requestedSalary" -> "< 1.750")
            += ("languages" -> List(
              Map("isoCode" -> "EN", "name" -> "Engels", "isNative"     -> false),
              Map("isoCode" -> "NL", "name" -> "Nederlands", "isNative" -> false),
              Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"     -> false)
            ))
            += ("availability"   -> "In overleg") += ("updatedDate" -> (last3Months + 100000))
            += ("driverLicenses" -> List(Map("type" -> "B - personenauto", "group" -> "B - personenauto")))).toMap
        )
      )
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("commute" -> Map("province" -> "Gelderland", "city" -> "Achterveld"))
          += ("functionGroups"  -> List("Directie/Management algemeen"))
          += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("HBO") += "workingHours" -> List("24 tot 32 uur")
          += ("careerLevel"     -> "Starter") += ("requestedSalary" -> "3.500 - 5.000")
          += ("availability"    -> "Per direct") += ("updatedDate"  -> (last24Hours + 100))
          += ("languages" -> List(
            Map("isoCode" -> "PR", "name" -> "Portugees", "isNative" -> false),
            Map("isoCode" -> "KN", "name" -> "Kantonees", "isNative" -> false),
            Map("isoCode" -> "EN", "name" -> "Engels", "isNative"    -> false)
          ))
          += ("driverLicenses" -> List(Map("type" -> "A - motor", "group" -> "A - motor")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("commute" -> Map("province" -> "Noord-Holland", "city" -> "Amsterdam"))
          += ("functionGroups"  -> List("Commercieel/Verkoop"))
          += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("MBO") += "workingHours" -> List("16 tot 24 uur")
          += ("careerLevel"     -> "Directie") += ("requestedSalary" -> "3.500 - 5.000")
          += ("availability"    -> "In overleg") += ("updatedDate"   -> (lastYear + 10000))
          += ("languages" -> List(
            Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"   -> false),
            Map("isoCode" -> "NL", "name" -> "Nederlands", "isNative" -> false),
            Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"     -> false)
          ))
          += ("driverLicenses" -> List(
            Map("type" -> "D - bus >8 personen", "group" -> "D - bus >8 personen"),
            Map("type" -> "C - vrachtwagen", "group"     -> "C - vrachtwagen")
          ))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "12",
        (document += ("commute" -> Map("province" -> "Groningen", "city" -> "Alteveer")) += ("functionGroups" -> List("Techniek"))
          += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("HBO") += "workingHours" -> List("16 tot 24 uur")
          += ("careerLevel"     -> "Leidinggevend") += ("requestedSalary"                                     -> "5.000 - 7.000")
          += ("availability"    -> "Per direct") += ("updatedDate"                                            -> (last6Months + 100000))
          += ("languages" -> List(
            Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"  -> false),
            Map("isoCode" -> "PR", "name" -> "Portugees", "isNative" -> false),
            Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"    -> false)
          ))
          += ("driverLicenses" -> List(Map("type" -> "D - bus >8 personen", "group" -> "D - bus >8 personen")))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "13",
        (document += ("commute" -> Map("province" -> "Zeeland", "city" -> "Eede"))
          += ("functionGroups"  -> List("Directie/Management algemeen"))
          += "preferredJobs" -> List("Scala developer") += "workLevels" -> List("MBO")
          += "workingHours"  -> List("Tot 16 uur", "16 tot 24 uur")
          += ("careerLevel"     -> "Starter") += ("requestedSalary" -> "5.000 - 7.000")
          += ("availability"    -> "In overleg") += ("updatedDate"  -> (last3Days + 100))
          += ("languages" -> List(
            Map("isoCode" -> "AR", "name" -> "Arabisch", "isNative"  -> false),
            Map("isoCode" -> "KN", "name" -> "Kantonees", "isNative" -> false),
            Map("isoCode" -> "SP", "name" -> "Spaans", "isNative"    -> false)
          ))
          += ("driverLicenses" -> List(Map("type" -> "B - personenauto", "group" -> "B - personenauto")))).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer")),
          CityFilter(domain.City("Eede")),
          ProvinceFilter(NonEmptyList.of(domain.Province("Noord-Holland"), domain.Province("Zeeland"))),
          UpdateDateFilter(domain.UpdateDate.Last6Months),
          FunctionGroupFilter(
            NonEmptyList.of(
              domain.FunctionGroup("Techniek"),
              domain.FunctionGroup("Directie/Management algemeen")
            )
          ),
          WorkLevelFilter(NonEmptyList.of(domain.WorkLevel("MBO"), domain.WorkLevel("HAVO"))),
          WorkingHourFilter(NonEmptyList.of(domain.WorkingHours.From16To24, domain.WorkingHours.From24To32)),
          CareerLevelFilter(NonEmptyList.of(domain.CareerLevel("Starter"), domain.CareerLevel("Directie"))),
          RequestedSalaryFilter(
            NonEmptyList.of(
              domain.RequestedSalary.From5000To7000,
              domain.RequestedSalary.From3500To5000
            )
          ),
          AvailabilityFilter(
            NonEmptyList.of(
              domain.Availability.InOverleg,
              domain.Availability.PerDirect
            )
          ),
          DriversLicenseFilter(
            NonEmptyList.of(
              domain.DriversLicense("B - personenauto"),
              domain.DriversLicense("D - bus >8 personen")
            )
          ),
          LanguageFilter(
            NonEmptyList.of(
              domain.Language("Arabisch"),
              domain.Language("Kantonees")
            )
          )
        )
      )
    } yield expect(
      searchResult.profiles.size == 1 &&
        searchResult.aggregations(AggregationKeyName("provinces")) == List(
          TermBucket("Gelderland", 0),
          TermBucket("Groningen", 0),
          TermBucket("Noord-Holland", 0),
          TermBucket("Zeeland", 1)
        ) &&
        searchResult.aggregations(AggregationKeyName("functionGroups")) == List(
          TermBucket("Commercieel/Verkoop", 0),
          TermBucket("Directie/Management algemeen", 1),
          TermBucket("Medisch/Zorg", 0),
          TermBucket("Techniek", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("workLevels")) == List(
          TermBucket("HBO", 0),
          TermBucket("MBO", 1),
          TermBucket("HAVO", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("workingHours")) == List(
          TermBucket("Tot 16 uur", 1),
          TermBucket("16 tot 24 uur", 1),
          TermBucket("24 tot 32 uur", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("careerLevels")) == List(
          TermBucket("Starter", 1),
          TermBucket("Leidinggevend", 0),
          TermBucket("Directie", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("requestedSalaries")) == List(
          TermBucket("< 1.750", 0),
          TermBucket("3.500 - 5.000", 0),
          TermBucket("5.000 - 7.000", 1)
        ) &&
        searchResult.aggregations(AggregationKeyName("availabilities")).sortBy(_.key) == List(
          TermBucket("In overleg", 1),
          TermBucket("Per direct", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("driverLicenses")) == List(
          TermBucket("A - motor", 0),
          TermBucket("B - personenauto", 1),
          TermBucket("C - vrachtwagen", 0),
          TermBucket("D - bus >8 personen", 0)
        ) &&
        searchResult.aggregations(AggregationKeyName("languages")).toSet == Set(
          TermBucket("Arabisch", 1),
          TermBucket("Engels", 0),
          TermBucket("Kantonees", 1),
          TermBucket("Nederlands", 0),
          TermBucket("Portugees", 0),
          TermBucket("Spaans", 1)
        ) &&
        searchResult.aggregations(AggregationKeyName("updatedDate")) == List(
          TermBucket("Afgelopen 24 uur", 0),
          TermBucket("Afgelopen 3 dagen", 1),
          TermBucket("Afgelopen week", 1),
          TermBucket("Afgelopen maand", 1),
          TermBucket("Afgelopen 3 maanden", 1),
          TermBucket("Afgelopen 6 maanden", 1),
          TermBucket("Afgelopen jaar", 1),
          TermBucket("Alles", 1)
        )
    )
  }

  test("It should sort profiles by 'updatedDate' field in descending order") { client =>
    val esIndex = UUID.randomUUID().toString
    val last3Days = ZonedDateTime.now().minus(3, ChronoUnit.DAYS).toEpochSecond + 100
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- (11 to 15).toList.traverse(counter =>
        insertDocument(client, esIndex, counter.toString, (document += (SortField.updatedDate.name -> (last3Days + counter))).toMap)
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        sortField = SortField.updatedDate.some
      )
    } yield expect(
      searchResult.profiles == List(
        profile.copy(id = ProfileId.unsafe("15"), updatedDate = UpdatedDate(last3Days + 15)),
        profile.copy(id = ProfileId.unsafe("14"), updatedDate = UpdatedDate(last3Days + 14)),
        profile.copy(id = ProfileId.unsafe("13"), updatedDate = UpdatedDate(last3Days + 13)),
        profile.copy(id = ProfileId.unsafe("12"), updatedDate = UpdatedDate(last3Days + 12)),
        profile.copy(id = ProfileId.unsafe("11"), updatedDate = UpdatedDate(last3Days + 11))
      )
    )
  }

  test("It should return notfound when a specific profile is not found") { client =>
    def visitedProfilesServiceImpl(attempts: IOLocal[Int]): VisitedProfilesService[IO] =
      visitedProfilesService((_, _) => attempts.update(x => x + 1) >> IO(New))

    for {
      attempts             <- IOLocal(0)
      esIndex              <- IO.randomUUID.map(_.toString)
      service              <- IO(profileSearchService(esIndex, client, visitedProfilesServiceImpl(attempts), viewedProfilesService()))
      _                    <- createESIndex(client, esIndex)
      document             <- service.getProfile(RecruiterId("1234"), ProfileId.unsafe("1")).attempt
      registrationAttempts <- attempts.get
    } yield expect(document == Left(ProfileNotFound(ProfileId.unsafe("1")))) and expect(registrationAttempts == 0)
  }

  test("It should return too many requests when the view limit was reached") { client =>
    def visitedProfilesServiceImpl(): VisitedProfilesService[IO] =
      visitedProfilesService((recruiterId, profileId) =>
        IO.raiseError(
          ViewsExceeded(recruiterId.into[visitedprofiles.RecruiterId].transform, profileId.into[visitedprofiles.ProfileId].transform)
        )
      )

    for {
      esIndex   <- IO.randomUUID.map(_.toString)
      service   <- IO(profileSearchService(esIndex, client, visitedProfilesServiceImpl(), viewedProfilesService()))
      _         <- createESIndex(client, esIndex)
      document  <- DataGenerator.generate
      profileId <- IO.randomUUID.map(_.toString)
      _         <- insertDocument(client, esIndex, profileId, document.toMap)
      result    <- service.getProfile(RecruiterId("1234"), ProfileId.unsafe(profileId)).attempt
    } yield expect(result == Left(ViewsExceeded(visitedprofiles.RecruiterId("1234"), visitedprofiles.ProfileId.unsafe(profileId))))
  }

  test("It should return a profile when it was viewed for the first time") { client =>
    for {
      esIndex   <- IO.randomUUID.map(_.toString)
      service   <- IO(profileSearchService(esIndex, client, visitedProfilesService((_, _) => IO(New)), viewedProfilesService()))
      _         <- createESIndex(client, esIndex)
      document  <- DataGenerator.generate
      profileId <- IO.randomUUID.map(_.toString)
      _         <- insertDocument(client, esIndex, profileId, document.toMap)
      result    <- service.getProfile(RecruiterId("1234"), ProfileId.unsafe(profileId)).attempt
    } yield result match {
      case Left(error) => failure(s"Expected to find the profile but got $error")
      case Right(_)    => success
    }
  }

  test("It should return a profile when it was viewed for the nth time") { client =>
    for {
      esIndex   <- IO.randomUUID.map(_.toString)
      _         <- createESIndex(client, esIndex)
      service   <- IO(profileSearchService(esIndex, client, visitedProfilesService((_, _) => IO(Existing)), viewedProfilesService()))
      profileId <- IO.randomUUID.map(_.toString)
      document  <- DataGenerator.generate
      _         <- insertDocument(client, esIndex, profileId, document.toMap)
      result    <- service.getProfile(RecruiterId("1234"), ProfileId.unsafe(profileId)).attempt
    } yield result match {
      case Left(error) => failure(s"Expected to find the profile but got $error")
      case Right(_)    => success
    }
  }

  test("It should return a profile even when the profile view storage has failed") { client =>
    for {
      esIndex <- IO.randomUUID.map(_.toString)
      _       <- createESIndex(client, esIndex)
      service <- IO(
        profileSearchService(
          esIndex,
          client,
          visitedProfilesService((_, _) => IO(Existing)),
          viewedProfilesService((_, _) => IO.raiseError(new Throwable("Something went wrong")))
        )
      )
      profileId <- IO.randomUUID.map(_.toString)
      document  <- DataGenerator.generate
      _         <- insertDocument(client, esIndex, profileId, document.toMap)
      result    <- service.getProfile(RecruiterId("1234"), ProfileId.unsafe(profileId)).attempt
    } yield result match {
      case Left(error) => failure(s"Expected to find the profile but got $error")
      case Right(_)    => success
    }
  }

  test("It should return not allowed when a recruiter was not entitled to view a profile") { client =>
    for {
      esIndex <- IO.randomUUID.map(_.toString)
      _       <- createESIndex(client, esIndex)
      service <- IO(
        profileSearchService(
          esIndex,
          client,
          visitedProfilesService((recruiterId, _) =>
            CorrelationId
              .generate[IO]
              .flatMap(cid => IO.raiseError(NotEntitled(recruiterId.into[creditservice.RecruiterId].transform, cid)))
          ),
          viewedProfilesService()
        )
      )
      profileId <- IO.randomUUID.map(_.toString)
      document  <- DataGenerator.generate
      _         <- insertDocument(client, esIndex, profileId, document.toMap)
      result    <- service.getProfile(RecruiterId("1234"), ProfileId.unsafe(profileId)).attempt
    } yield result match {
      case Left(NotEntitled(_, _)) => success
      case _                       => failure("Expected to get a forbidden error")
    }
  }

  test("It should provide suggestions for city names") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service           <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _                 <- createESIndex(client, esIndex)
      document          <- DataGenerator.generate
      _                 <- insertDocument(client, esIndex, "1", (document += ("commute" -> Map("city" -> "Amsterdam"))).toMap)
      _                 <- insertDocument(client, esIndex, "2", (document += ("commute" -> Map("city" -> "Amstelveen"))).toMap)
      _                 <- insertDocument(client, esIndex, "3", (document += ("commute" -> Map("city" -> "Amstelhoek"))).toMap)
      _                 <- insertDocument(client, esIndex, "4", (document += ("commute" -> Map("city" -> "Utrecht"))).toMap)
      _                 <- insertDocument(client, esIndex, "5", (document += ("commute" -> Map("city" -> "Den Haag"))).toMap)
      firstSuggestions  <- service.getCitySuggestions(CityPrefix("Ams"))
      secondSuggestions <- service.getCitySuggestions(CityPrefix("Amstel"))
    } yield expect(
      firstSuggestions == List(CitySuggestion("Amsterdam"), CitySuggestion("Amstelveen"), CitySuggestion("Amstelhoek")) &&
        secondSuggestions == List(CitySuggestion("Amstelveen"), CitySuggestion("Amstelhoek"))
    )
  }

  test("It should provide suggestions with case insensitive prefixes") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service     <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _           <- createESIndex(client, esIndex)
      document    <- DataGenerator.generate
      _           <- insertDocument(client, esIndex, "1", (document += ("commute" -> Map("city" -> "Amsterdam"))).toMap)
      _           <- insertDocument(client, esIndex, "2", (document += ("commute" -> Map("city" -> "Amstelveen"))).toMap)
      _           <- insertDocument(client, esIndex, "3", (document += ("commute" -> Map("city" -> "Amstelhoek"))).toMap)
      _           <- insertDocument(client, esIndex, "4", (document += ("commute" -> Map("city" -> "Utrecht"))).toMap)
      _           <- insertDocument(client, esIndex, "5", (document += ("commute" -> Map("city" -> "Den Haag"))).toMap)
      suggestions <- service.getCitySuggestions(CityPrefix("ams"))
    } yield expect(
      suggestions == List(CitySuggestion("Amsterdam"), CitySuggestion("Amstelveen"), CitySuggestion("Amstelhoek"))
    )
  }

  test("It should return distinct city name suggestions") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service     <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _           <- createESIndex(client, esIndex)
      document    <- DataGenerator.generate
      _           <- insertDocument(client, esIndex, "1", (document += ("commute" -> Map("city" -> "Amsterdam"))).toMap)
      _           <- insertDocument(client, esIndex, "2", (document += ("commute" -> Map("city" -> "Amsterdam"))).toMap)
      _           <- insertDocument(client, esIndex, "3", (document += ("commute" -> Map("city" -> "Amsterdam"))).toMap)
      _           <- insertDocument(client, esIndex, "4", (document += ("commute" -> Map("city" -> "Utrecht"))).toMap)
      _           <- insertDocument(client, esIndex, "5", (document += ("commute" -> Map("city" -> "Den Haag"))).toMap)
      suggestions <- service.getCitySuggestions(CityPrefix("Ams"))
    } yield expect(suggestions == List(CitySuggestion("Amsterdam")))
  }

  test("It should not return any suggestions for city names when the prefix does not match the stored cities") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service     <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _           <- createESIndex(client, esIndex)
      document    <- DataGenerator.generate
      _           <- insertDocument(client, esIndex, "1", (document += ("commute" -> Map("city" -> "Amsterdam"))).toMap)
      _           <- insertDocument(client, esIndex, "2", (document += ("commute" -> Map("city" -> "Amstelveen"))).toMap)
      _           <- insertDocument(client, esIndex, "3", (document += ("commute" -> Map("city" -> "Amstelhoek"))).toMap)
      _           <- insertDocument(client, esIndex, "4", (document += ("commute" -> Map("city" -> "Utrecht"))).toMap)
      _           <- insertDocument(client, esIndex, "5", (document += ("commute" -> Map("city" -> "Den Haag"))).toMap)
      suggestions <- service.getCitySuggestions(CityPrefix("UnknownPrefix"))
    } yield expect(suggestions == List())
  }

  test("It should return relevant results when searching a the attachment content") { client =>
    for {
      esIndex  <- IO.randomUUID.map(_.toString)
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("attachmentContents" -> List(
          Map(
            "id"      -> "2cffc788-145f-11f0-b6be-4bfbbe17acb1",
            "content" -> "Ik ben een ervaren software engineer met ervaring in Scala en Java"
          )
        ))).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("attachmentContents" -> List(
          Map(
            "id"      -> "33c556aa-145f-11f0-98fe-b73d18128e06",
            "content" -> "Ik ben een ervaren software engineer met ervaring in Python"
          )
        ))).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala"))
        )
      )
    } yield expect(
      searchResult.profiles.size == 1 &&
        searchResult.profiles.head.id == ProfileId.unsafe("10")
    )
  }

  test("It should not return profiles that are not exposed") { client =>
    val esIndex = UUID.randomUUID().toString
    for {
      service  <- IO(profileSearchService(esIndex, client, visitedProfilesService(), viewedProfilesService()))
      _        <- createESIndex(client, esIndex)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client,
        esIndex,
        "10",
        (document += ("findable" -> false) += "preferredJobs" -> List("Scala developer")).toMap
      )
      _ <- insertDocument(
        client,
        esIndex,
        "11",
        (document += ("findable" -> true) += "preferredJobs" -> List("Scala developer")).toMap
      )
      pagination <- pagination(1, 100)
      searchResult <- service.getProfiles(
        pagination = pagination,
        filters = List(
          SearchTermFilter(domain.SearchTerm.unsafe("Scala developer"))
        )
      )
    } yield expect(
      searchResult.profiles.size == 1 &&
        searchResult.profiles.head.id == ProfileId.unsafe("11")
    )
  }

  test("It should search jobs by id") { client =>
    def addDocument(indexName: String): IO[ProfileId] = for {
      id       <- IO.randomUUID.map(_.toString)
      document <- DataGenerator.generate
      _ <- insertDocument(
        client = client,
        index = indexName,
        id = id,
        document = document.toMap
      )
    } yield ProfileId.unsafe(id)

    for {
      indexName <- IO.randomUUID.map(_.toString)
      service   <- IO(profileSearchService(indexName, client, visitedProfilesService(), viewedProfilesService()))
      _         <- createESIndex(client, indexName)
      ids       <- (1 to 10).toList.traverse(_ => addDocument(indexName))
      _         <- IO.sleep(1.second)

      shuffledIds = Random.shuffle(ids)
      _ <- IO.println(s"Shuffled ids: $shuffledIds")

      searchResult <- service.getProfilesById(shuffledIds, None)
    } yield expect(searchResult.profiles.map(_.id) == shuffledIds) and expect(searchResult.profiles.size == 10)
  }
}
