package nl.dpes.recruiterfavorites.service

import cats.data.NonEmptyList
import cats.effect.{IO, Resource}
import doobie.*
import doobie.implicits.*
import doobie.util.transactor.Transactor
import io.scalaland.chimney.syntax.transformInto
import nl.dpes.recruiterfavorites.recruiterfavoritesstorage.MysqlFavoritesRepository
import nl.dpes.recruiterfavorites.recruiterfavoritesstorage.dbmodel.{ProfileId as DbProfileId, RecruiterId as DbRecruiterId}
import nl.dpes.recruiterfavorites.recruiterfavoritesstorage.config.FavoriteProfilesConfig.TableName
import nl.dpes.recruiterfavorites.service.domainmodel.{PaginatedProfileIds, ProfileId, RecruiterId}
import nl.dpes.recruiterfavorites.testcontainers.MySqlDatabaseGenerator
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.IOSuite

import java.util.UUID

object FavoritesRepositorySpec extends IOSuite with MySqlDatabaseGenerator {

  override type Res = Transactor[IO]

  override def sharedResource: Resource[IO, FavoritesRepositorySpec.Res] = transactor

  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  case class Repo(favoritesRepo: FavoritesRepository[IO], tableName: TableName)

  private val recruiterId: RecruiterId = RecruiterId("This a recruiter id")
  private val profileId: ProfileId = ProfileId("profile 1")
  private val profileId2: ProfileId = ProfileId("profile 2")
  private val profileId3: ProfileId = ProfileId("profile 3")

  def randomRepo(xa: Transactor[IO]): Resource[IO, Repo] = {
    val id = UUID.randomUUID().toString.replace("-", "")
    for {
      repo <- MysqlFavoritesRepository.resource(TableName(s"favorites_$id".trim), xa)
    } yield Repo(repo, TableName(s"favorites_$id".trim))
  }

  test("It should be able to create the table in an idempotent way") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _       <- repo.favoritesRepo.initialize.attempt
        result  <- repo.favoritesRepo.initialize.attempt
        columns <- getColumnNames(Fragment.const(repo.tableName.value), xa)
      } yield expect(
        result == Right(()) &&
          columns.sorted == List(
            "profileId",
            "recruiterId",
            "timestamp"
          )
      )
    }
  }

  test("It should be able to save favorite profiles") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId2).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId3).attempt
        _         <- repo.favoritesRepo.saveFavorite(RecruiterId("recruiter 2"), profileId).attempt
        favorites <- getProfiles(recruiterId, Fragment.const(repo.tableName.value), xa)
      } yield expect(favorites == List(profileId, profileId2, profileId3))
    }
  }

  test("It should not fail when trying to save the same favorite profile more than once") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        favorites <- getProfiles(recruiterId, Fragment.const(repo.tableName.value), xa)
      } yield expect(favorites == List(profileId))
    }
  }

  test("It should fail when unknown error happens while trying to save favorites in the table") { xa =>
    randomRepo(xa).use { repo =>
      for {
        result <- repo.favoritesRepo.saveFavorite(recruiterId, ProfileId("UNKNOWN" * 1000)).attempt
      } yield expect(result.isLeft)
    }
  }

  test("It should be able to get favorites by id list") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _ <- repo.favoritesRepo.saveFavorite(recruiterId, ProfileId("profile 1")).attempt
        _ <- repo.favoritesRepo.saveFavorite(recruiterId, profileId2).attempt
        _ <- repo.favoritesRepo.saveFavorite(recruiterId, profileId3).attempt
        _ <- repo.favoritesRepo.saveFavorite(RecruiterId("recruiter 2"), ProfileId("profile 1")).attempt
        favorites <- repo.favoritesRepo.getFavoritesByIds(
          recruiterId,
          NonEmptyList.of(ProfileId("profile 1"), profileId2, profileId3)
        )
      } yield expect(favorites == List(ProfileId("profile 1"), profileId2, profileId3))
    }
  }

  test("it should not find other recruiters' favorites when getting favorites by id list") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _ <- repo.favoritesRepo.saveFavorite(recruiterId, ProfileId("profile 1")).attempt
        _ <- repo.favoritesRepo.saveFavorite(recruiterId, profileId2).attempt
        _ <- repo.favoritesRepo.saveFavorite(recruiterId, profileId3).attempt
        _ <- repo.favoritesRepo.saveFavorite(RecruiterId("recruiter 2"), ProfileId("profile 1")).attempt
        favorites <- repo.favoritesRepo.getFavoritesByIds(
          RecruiterId("recruiter 2"),
          NonEmptyList.of(ProfileId("profile 1"), profileId2, profileId3)
        )
      } yield expect(favorites == List(ProfileId("profile 1")))
    }
  }

  test("It should return an empty list when no favorites are found for the given recruiter") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _ <- repo.favoritesRepo.saveFavorite(recruiterId, ProfileId("profile 1")).attempt
        favorites <- repo.favoritesRepo.getFavoritesByIds(
          RecruiterId("non-existing-recruiter"),
          NonEmptyList.of(ProfileId("profile 1"), profileId2)
        )
      } yield expect(favorites.isEmpty)
    }
  }

  test("It should be able to delete favorite profiles") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId2).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId3).attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, profileId2).attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, profileId3).attempt
        favorites <- getProfiles(recruiterId, Fragment.const(repo.tableName.value), xa)
      } yield expect(favorites == List(profileId))
    }
  }

  test("It should not fail when trying to delete non-existing profiles") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, profileId2).attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, profileId3).attempt
        favorites <- getProfiles(recruiterId, Fragment.const(repo.tableName.value), xa)
      } yield expect(favorites == List(profileId))
    }
  }

  test("It should be able to delete favorite profiles") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId2).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId3).attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, profileId2).attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, profileId3).attempt
        favorites <- getProfiles(recruiterId, Fragment.const(repo.tableName.value), xa)
      } yield expect(favorites == List(profileId))
    }
  }

  test("It should not fail when trying to delete non-existing profiles") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, profileId2).attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, profileId3).attempt
        favorites <- getProfiles(recruiterId, Fragment.const(repo.tableName.value), xa)
      } yield expect(favorites == List(profileId))
    }
  }

  test("It should be able to get favorites for a recruiter") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId2).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId3).attempt
        favorites <- repo.favoritesRepo.getFavoritesForRecruiter(recruiterId)
      } yield expect(favorites == PaginatedProfileIds(List(profileId, profileId2, profileId3), 3))
    }
  }

  test("It should be able to paginate favorites for a recruiter") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _                   <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _                   <- repo.favoritesRepo.saveFavorite(recruiterId, profileId2).attempt
        _                   <- repo.favoritesRepo.saveFavorite(recruiterId, profileId3).attempt
        firstPageFavorites  <- repo.favoritesRepo.getFavoritesForRecruiter(recruiterId = recruiterId, page = 1, limit = 2)
        secondPageFavorites <- repo.favoritesRepo.getFavoritesForRecruiter(recruiterId = recruiterId, page = 2, limit = 2)
      } yield expect(firstPageFavorites == PaginatedProfileIds(List(profileId, profileId2), 3)) && expect(
        secondPageFavorites == PaginatedProfileIds(List(profileId3), 3)
      )
    }
  }

  test("It should be able to check if a profile was set as favorite or not") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _           <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        isFavorite  <- repo.favoritesRepo.isFavorite(recruiterId, profileId)
        notFavorite <- repo.favoritesRepo.isFavorite(recruiterId, profileId2)
      } yield expect(isFavorite) && expect(!notFavorite)
    }
  }

  def getColumnNames(tableName: Fragment, xa: Transactor[IO]): IO[List[String]] =
    sql"""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = '$tableName'
       """
      .query[String]
      .to[List]
      .transact(xa)

  def getProfiles(recruiterId: RecruiterId, tableName: Fragment, xa: Transactor[IO]): IO[List[ProfileId]] =
    sql"""
        SELECT profileId
        FROM $tableName
        WHERE recruiterId = ${recruiterId.transformInto[DbRecruiterId]}
        ORDER BY timestamp
       """
      .query[DbProfileId]
      .to[List]
      .map(_.map(_.transformInto[ProfileId]))
      .transact(xa)
}
