package nl.dpes.recruiterfavorites.service

import cats.data.NonEmptyList
import cats.effect.IO
import nl.dpes.recruiterfavorites.service.domainmodel.*
import org.scalamock.stubs.Stubs
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.SimpleIOSuite

object FavoritesServiceSpec extends SimpleIOSuite with Stubs {

  case class Error(message: String) extends Throwable(message)

  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  val unit: Unit = ()
  val recruiterId: RecruiterId = RecruiterId("ABCDEFGHIJ12345678")
  val profileId: ProfileId = ProfileId("550e8400-e29b-41d4-a716-446655440000")

  def favoritesRepository(
    saveFavoriteFn: => IO[Unit] = IO.unit,
    deleteFavoriteFn: => IO[Unit] = IO.unit,
    isFavoriteFn: => IO[Boolean] = IO(true),
    getFavoritesByIdsFn: => IO[List[ProfileId]] = IO(List()),
    getFavoritesByForRecruiter: => IO[PaginatedProfileIds] = IO(PaginatedProfileIds(List(), 0L))
  ): FavoritesRepository[IO] = new FavoritesRepository[IO] {
    override def initialize: IO[Unit] = IO.unit

    override def saveFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Unit] = saveFavoriteFn

    override def deleteFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Unit] = deleteFavoriteFn

    override def isFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Boolean] = isFavoriteFn

    override def getFavoritesByIds(recruiterId: RecruiterId, profileIds: NonEmptyList[ProfileId]): IO[List[ProfileId]] =
      getFavoritesByIdsFn

    override def getFavoritesForRecruiter(recruiterId: RecruiterId, page: Long = 1, limit: Long = 10): IO[PaginatedProfileIds] =
      getFavoritesByForRecruiter
  }

  test("It should be able to save favorite profiles") {
    val stub = favoritesRepository()
    val service = FavoritesService.impl(stub)
    for {
      result <- service.saveFavorite(recruiterId, profileId)
    } yield expect(result == unit)
  }

  test("It should return an error when unable to save favorite profiles") {
    val error = Error("Something went wrong")
    val stub = favoritesRepository(saveFavoriteFn = IO.raiseError(error))
    val service = FavoritesService.impl(stub)
    for {
      result <- service.saveFavorite(recruiterId, profileId).attempt
    } yield expect(result == Left(error))
  }

  test("It should be able to delete favorite profiles") {
    val stub = favoritesRepository()
    val service = FavoritesService.impl(stub)
    for {
      result <- service.deleteFavorite(recruiterId, profileId)
    } yield expect(result == unit)
  }

  test("It should return an error when unable to delete favorite profiles") {
    val error = Error("Something went wrong")
    val stub = favoritesRepository(deleteFavoriteFn = IO.raiseError(error))
    val service = FavoritesService.impl(stub)
    for {
      result <- service.deleteFavorite(recruiterId, profileId).attempt
    } yield expect(result == Left(error))
  }

  test("It should be able to filter favorite profiles by ids") {
    val profileIds = List(ProfileId("profile_1"), ProfileId("profile_2"))
    val dbProfileIds = NonEmptyList.of(ProfileId("profile_1"), ProfileId("profile_2"))
    val stub = favoritesRepository(getFavoritesByIdsFn = IO.pure(dbProfileIds.toList))
    val service = FavoritesService.impl(stub)
    for {
      result <- service.filterFavorites(recruiterId, profileIds)
    } yield expect(result == profileIds)
  }

  test("It should return an empty list when no profile ids are provided") {
    val stub = favoritesRepository(getFavoritesByIdsFn = IO.raiseError(Error("No profile ids provided"))) // this should not be called
    val service = FavoritesService.impl(stub)
    for {
      result <- service.filterFavorites(recruiterId, List())
    } yield expect(result == List())
  }

  test("It should return paginated favorites for a recruiter") {
    val profileIdString = "1234567890"
    val favoriteRepository = stub[FavoritesRepository[IO]]
    favoriteRepository.getFavoritesForRecruiter
      .returns(_ => IO.pure(PaginatedProfileIds(List(ProfileId(profileIdString)), 1L)))

    val service = FavoritesService.impl(favoriteRepository)
    for {
      result <- service.getFavoritesForRecruiter(recruiterId, 1, 5)
    } yield expect(result == PaginatedProfileIds(List(ProfileId(profileIdString)), 1L))
      && expect(
        favoriteRepository.getFavoritesForRecruiter.calls == List(
          (recruiterId, 1L, 5)
        )
      )
  }

  test("It should check if a profile was set as favorite or not") {
    val repo = favoritesRepository()
    val service = FavoritesService.impl(repo)

    for {
      isFavorite <- service.isFavorite(recruiterId, profileId)
    } yield expect(isFavorite)
  }

  test("It should return an error when unable to check if a profile was set as favorite or not") {
    val repo = favoritesRepository(isFavoriteFn = IO.raiseError(Error("Something went wrong")))
    val service = FavoritesService.impl(repo)

    for {
      result <- service.isFavorite(recruiterId, profileId).attempt
    } yield expect(result == Left(Error("Something went wrong")))
  }
}
