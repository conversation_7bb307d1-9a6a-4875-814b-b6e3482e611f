package nl.dpes.profileservice.savedsearch

import cats.effect.IO
import nl.dpes.common.utils.Responses.BadRequest
import nl.dpes.profileservice.savedsearch.apimodel.{
  Frequency,
  RecruiterId,
  SavedSearch,
  SavedSearch<PERSON>reation,
  SavedSearchId,
  SavedSearchName,
  SavedSearchUpdate,
  SearchFilters
}
import sttp.client3.{basicRequest, UriContext}
import sttp.tapir.integ.cats.effect.CatsMonadError
import sttp.client3.testing.SttpBackendStub
import sttp.model.StatusCode
import sttp.tapir.server.stub.TapirStubInterpreter
import weaver.SimpleIOSuite

object SavedSearchControllerSpec extends SimpleIOSuite {

  val unit: Unit = ()
  val savedSearchId: SavedSearchId = SavedSearchId("123456789")
  val recruiterId: RecruiterId = RecruiterId.unsafeApply("123456789123456789")
  val invalidRecruiterId: RecruiterId = RecruiterId.unsafeApply("123")
  val savedSearchName: SavedSearchName = SavedSearchName("Scala developer")
  val frequency: Frequency = Frequency.unsafeApply("Nooit")
  val invalidFrequency: Frequency = Frequency.unsafeApply("unknown")
  val searchFilters: SearchFilters = SearchFilters(
    searchTerm = None,
    city = None,
    geoDistance = None,
    provinces = None,
    updatedDate = None,
    functionGroups = None,
    workLevels = None,
    workingHours = None,
    careerLevels = None,
    requestedSalaries = None,
    availabilities = None,
    driversLicenses = None,
    languages = None
  )

  val savedSearchCreation: SavedSearchCreation = SavedSearchCreation(savedSearchName, frequency, searchFilters)
  val savedSearchUpdate: SavedSearchUpdate = SavedSearchUpdate(frequency)
  val savedSearch: SavedSearch =
    SavedSearch(id = savedSearchId, recruiterId = recruiterId, name = savedSearchName, frequency = frequency, searchFilters = searchFilters)

  def savedSearchService(
    getByIdFn: => IO[Either[BadRequest, SavedSearch]] = IO(Right(savedSearch)),
    createFn: => IO[Either[BadRequest, Unit]] = IO(Right(unit)),
    updateFn: => IO[Either[BadRequest, Unit]] = IO(Right(unit)),
    deleteFn: => IO[Either[BadRequest, Unit]] = IO(Right(unit)),
    getAllFn: => IO[Either[BadRequest, List[SavedSearch]]] = IO(Right(List(savedSearch)))
  ): SavedSearchControllerService[IO] = new SavedSearchControllerService[IO] {
    override def getById(recruiterId: RecruiterId, id: SavedSearchId): IO[Either[BadRequest, SavedSearch]] = getByIdFn

    override def create(recruiterId: RecruiterId, searchFilters: SavedSearchCreation): IO[Either[BadRequest, Unit]] = createFn

    override def update(recruiterId: RecruiterId, id: SavedSearchId, update: SavedSearchUpdate): IO[Either[BadRequest, Unit]] = updateFn

    override def delete(recruiterId: RecruiterId, id: SavedSearchId): IO[Either[BadRequest, Unit]] = deleteFn

    override def getAll(recruiterId: RecruiterId): IO[Either[BadRequest, List[SavedSearch]]] = getAllFn
  }

  test("It should be able to get a saved search by its id for a recruiter") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.getById
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/savedsearches/${savedSearchId.value}")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.Ok)
  }

  test("It should return BadRequest when providing an invalid recruiter id while trying to get a saved search") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.getById
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/savedsearches/${savedSearchId.value}")
        .header("X-Recruiter-ID", invalidRecruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should be able to save a search for a recruiter") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.create
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .post(uri"http://localhost:14200/savedsearches")
        .header("X-Recruiter-ID", recruiterId.value)
        .body(s"""{"name":"$savedSearchName","frequency":"${frequency.value}","searchFilters":{}}""")
        .send(backendStub)
    } yield expect(response.code == StatusCode.Created)
  }

  test("It should return BadRequest when providing an invalid recruiter id while trying to save a search") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.create
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .post(uri"http://localhost:14200/savedsearches")
        .header("X-Recruiter-ID", invalidRecruiterId.value)
        .body(s"""{"name":"$savedSearchName","frequency":"${frequency.value}","searchFilters":{}}""")
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should be able to update the frequency of a saved search for a recruiter") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.update
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .patch(uri"http://localhost:14200/savedsearches/${savedSearchId.value}")
        .header("X-Recruiter-ID", recruiterId.value)
        .body(s"""{"frequency":"${frequency.value}"}""")
        .send(backendStub)
    } yield expect(response.code == StatusCode.Ok)
  }

  test("It should return BadRequest when providing an invalid frequency while trying to update a saved search") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.update
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .patch(uri"http://localhost:14200/savedsearches/${savedSearchId.value}")
        .header("X-Recruiter-ID", recruiterId.value)
        .body(s"""{"frequency":"${invalidFrequency.value}"}""")
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return a BadRequest when providing an invalid recruiter id while trying to update a saved search") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.update
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .patch(uri"http://localhost:14200/savedsearches/${savedSearchId.value}")
        .header("X-Recruiter-ID", invalidRecruiterId.value)
        .body(s"""{"frequency":"${frequency.value}"}""")
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should be able to delete a saved search for a recruiter") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.delete
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .delete(uri"http://localhost:14200/savedsearches/${savedSearchId.value}")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.Ok)
  }

  test("It should return BadRequest when providing an invalid recruiter id while trying to delete a saved search") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.delete
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .delete(uri"http://localhost:14200/savedsearches/${savedSearchId.value}")
        .header("X-Recruiter-ID", invalidRecruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should be able to get all saved searches for a recruiter") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.getAll
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/savedsearches")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.Ok)
  }

  test("It should return BadRequest when providing an invalid recruiter id while trying to get all the saved searches") {
    for {
      controller     <- IO(SavedSearchController.impl[IO](savedSearchService()))
      serverEndpoint <- controller.getAll
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/savedsearches")
        .header("X-Recruiter-ID", invalidRecruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }
}
