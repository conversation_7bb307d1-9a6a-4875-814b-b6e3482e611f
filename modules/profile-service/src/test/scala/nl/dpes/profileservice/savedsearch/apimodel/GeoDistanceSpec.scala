package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.{GeoDistance, Latitude, Longitude}
import weaver.FunSuite

object GeoDistanceSpec extends FunSuite {

  val geoDistance = """{"latitude":"10.10","longitude":"30.30"}"""

  test("Encode Geo distance") {
    expect(GeoDistance(Latitude("10.10"), Longitude("30.30")).asJson.noSpaces == geoDistance)
  }

  test("GeoDistance should be encoded and decoded correctly") {
    val city = GeoDistance(Latitude("10.10"), Longitude("30.30"))

    val roundTrippedGeoDistance = decode[GeoDistance](city.asJson.noSpaces)

    expect(roundTrippedGeoDistance == Right(city))
  }
}
