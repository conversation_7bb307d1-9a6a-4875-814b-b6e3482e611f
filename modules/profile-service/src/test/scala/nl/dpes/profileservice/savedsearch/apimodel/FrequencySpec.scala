package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.Frequency
import weaver.FunSuite

object FrequencySpec extends FunSuite {

  test("Frequency should be json encoded as a string") {
    val frequency = Frequency("Dagelijks").toOption.get
    val json = frequency.asJson

    expect(json == "Dagelijks".asJson)
  }

  test("Frequency should be encoded and decoded correctly") {
    val frequency = Frequency("Dagelijks").toOption.get

    val roundTrippedFrequency = decode[Frequency](frequency.asJson.noSpaces)

    expect(roundTrippedFrequency == Right(frequency))
  }

  test("Unable to decode an invalid frequency") {
    expect(
      Json.fromString("unknown").as[Frequency] == Left(
        DecodingFailure(
          s"The provided frequency 'unknown' is not supported. The possible values are '${Frequency.supportedValues}'",
          List()
        )
      )
    )
  }
}
