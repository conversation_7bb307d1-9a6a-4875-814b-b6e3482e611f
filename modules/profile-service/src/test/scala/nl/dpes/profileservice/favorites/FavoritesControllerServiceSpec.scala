package nl.dpes.profileservice.favorites

import cats.effect.IO
import nl.dpes.profilesearch.service.ProfileSearchService
import nl.dpes.profilesearch.service.model.profilesearch.{LastViewedDate, ProfileSearchResult, SearchResult}
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profileservice.favorites.apimodel.{FavoriteResponse, ProfileId as ApiProfileId, RecruiterId as ApiRecruiterId}
import nl.dpes.recruiterfavorites.service.FavoritesService as DomainFavoritesService
import nl.dpes.recruiterfavorites.service.domainmodel.*
import org.scalamock.stubs.Stubs
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.SimpleIOSuite

object FavoritesControllerServiceSpec extends SimpleIOSuite with Stubs {
  case class Error(message: String) extends Throwable(message)

  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  val unit: Unit = ()
  val recruiterId: ApiRecruiterId = ApiRecruiterId("ABCDEFGHIJ12345678").toOption.get
  val profileId: ApiProfileId = ApiProfileId("550e8400-e29b-41d4-a716-446655440000").toOption.get
  val lastViewedDate: LastViewedDate = LastViewedDate(1751958970)

  def favoritesService(
    saveFavoriteFn: => IO[Unit] = IO.unit,
    isFavoriteFn: => IO[Boolean] = IO(true)
  ): DomainFavoritesService[IO] = new DomainFavoritesService[IO] {
    override def saveFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Unit] = saveFavoriteFn

    override def filterFavorites(recruiterId: RecruiterId, profileIds: List[ProfileId]): IO[List[ProfileId]] =
      IO(List())

    override def deleteFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Unit] = IO.unit

    override def isFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Boolean] = isFavoriteFn

    override def getFavoritesForRecruiter(recruiterId: RecruiterId, page: Long = 1, limit: Long = 10): IO[PaginatedProfileIds] =
      IO.pure(PaginatedProfileIds(List.empty, 0L))
  }

  private val profileService = stub[ProfileSearchService[IO]]

  test("It should be able to save favorite profiles") {
    val favoriteService = favoritesService()
    val service = FavoritesControllerService.impl(favoriteService, profileService)
    for {
      result <- service.saveFavorite(recruiterId, profileId)
    } yield expect(result == Right(unit))
  }

  test("It should return an error when unable to save favorite profiles") {
    val error = Error("Something went wrong")
    val favoriteService = favoritesService(saveFavoriteFn = IO.raiseError(error))
    val service = FavoritesControllerService.impl(favoriteService, profileService)
    for {
      result <- service.saveFavorite(recruiterId, profileId).attempt
    } yield expect(result == Left(error))
  }

  test("It should be able to delete favorite profiles") {
    val favoriteService = favoritesService()
    val service = FavoritesControllerService.impl(favoriteService, profileService)
    for {
      result <- service.deleteFavorite(recruiterId, profileId)
    } yield expect(result == Right(unit))
  }

  test("It should return favorites for a recruiter") {
    val favoriteService = stub[DomainFavoritesService[IO]]
    val service = FavoritesControllerService.impl(favoriteService, profileService)

    val profileIdString = "1234567890"

    favoriteService.getFavoritesForRecruiter.returns(_ =>
      IO.pure(PaginatedProfileIds(profileId = List(ProfileId(profileIdString)), total = 1))
    )
    profileService.getProfilesById.returns(_ =>
      IO.pure(
        SearchResult(
          profiles = List(
            ProfileSearchResult(
              id = profilesearch.ProfileId.unsafe(profileIdString),
              name = None,
              updatedDate = profilesearch.UpdatedDate(123),
              workingHours = None,
              workLevels = List.empty,
              experiences = List.empty,
              preferredJobs = List.empty,
              photo = None,
              city = None,
              lastViewedDate = Some(lastViewedDate)
            )
          ),
          aggregations = Map.empty,
          totalNumberOfProfiles = 1
        )
      )
    )

    val profiles = List(
      nl.dpes.profileservice.search.searchmodel.ProfileSearchResult(
        id = nl.dpes.profileservice.search.ProfileId.unsafeApply(profileIdString),
        name = None,
        updatedDate = nl.dpes.profileservice.search.searchmodel.UpdatedDate(123),
        workingHours = None,
        workLevels = List.empty,
        experiences = List.empty,
        preferredJobs = List.empty,
        photo = None,
        city = None,
        isFavorite = true,
        lastViewedDate = Some(nl.dpes.profileservice.search.searchmodel.LastViewedDate(lastViewedDate.timestamp))
      )
    )

    for {
      result <- service.getFavorites(recruiterId)
    } yield expect(result == Right(FavoriteResponse(profiles, 1)))
      && expect(favoriteService.getFavoritesForRecruiter.calls == List((recruiterId.value, 1, 10)))
      && expect(profileService.getProfilesById.calls == List((List(profileIdString), Some(recruiterId.value))))
  }

  test("It should return an error when unable to get favorites for a recruiter") {
    val error = Error("Something went wrong")
    val favoriteService = stub[DomainFavoritesService[IO]]
    val service = FavoritesControllerService.impl(favoriteService, profileService)

    favoriteService.getFavoritesForRecruiter.returns(_ => IO.raiseError(error))

    for {
      result <- service.getFavorites(recruiterId).attempt
    } yield expect(result == Left(error))
  }
}
