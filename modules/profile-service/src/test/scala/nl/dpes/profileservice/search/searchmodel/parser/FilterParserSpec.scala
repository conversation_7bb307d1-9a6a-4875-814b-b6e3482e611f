package nl.dpes.profileservice
package search.searchmodel.parser

import cats.data.*
import nl.dpes.profilesearch.domain.filter.*
import nl.dpes.profileservice.search.searchmodel.filter.*
import nl.dpes.profileservice.search.searchmodel.{ProfileSearchFilters, SearchTerm}
import nl.dpes.profilesearch.domain
import weaver.FunSuite

object FilterParserSpec extends FunSuite {
  test("The filter parser should parse filters correctly") {
    val filters: ProfileSearchFilters = ProfileSearchFilters(
      searchTerm = Some(SearchTerm("developer")),
      location = Some(Location("Amsterdam")),
      geoDistance = Some(GeoDistance("1.12, 56.33")),
      provinces = List(Province("North Holland"), Province("South Holland")),
      updatedDate = Some(UpdatedDate("Afgelopen 3 dagen")),
      functionGroups = List(FunctionGroup("IT"), FunctionGroup("Engineering")),
      workLevels = List(WorkLevel("Senior"), WorkLevel("Mid-level")),
      workingHours = List(WorkingHour("16 tot 24 uur"), WorkingHour("24 tot 32 uur")),
      careerLevels = List(CareerLevel("Experienced"), CareerLevel("Entry-level")),
      requestedSalaries = List(RequestedSalary("2.500 - 3.500"), RequestedSalary("3.500 - 5.000")),
      availabilities = List(Availability("Per direct"), Availability("In overleg")),
      driverLicenses = List(DriversLicense("B"), DriversLicense("C")),
      languages = List(Language("English"), Language("Dutch"))
    )
    val parsedFilters: ValidatedNel[Throwable, List[Filter]] = FilterParser.parseFilters(filters)
    val expectedFilters: ValidatedNel[Throwable, List[Filter]] = Validated.valid(
      List(
        FindableFilter,
        SearchTermFilter(domain.SearchTerm.unsafe("developer*")),
        CityFilter(domain.City("Amsterdam")),
        GeoDistanceFilter(1.12, 56.33),
        ProvinceFilter(NonEmptyList(domain.Province("North Holland"), List(domain.Province("South Holland")))),
        UpdateDateFilter(domain.UpdateDate.Last3Days),
        FunctionGroupFilter(NonEmptyList(domain.FunctionGroup("IT"), List(domain.FunctionGroup("Engineering")))),
        WorkLevelFilter(NonEmptyList(domain.WorkLevel("Senior"), List(domain.WorkLevel("Mid-level")))),
        WorkingHourFilter(NonEmptyList(domain.WorkingHours.From16To24, List(domain.WorkingHours.From24To32))),
        CareerLevelFilter(NonEmptyList(domain.CareerLevel("Experienced"), List(domain.CareerLevel("Entry-level")))),
        RequestedSalaryFilter(NonEmptyList(domain.RequestedSalary.From2500To3500, List(domain.RequestedSalary.From3500To5000))),
        AvailabilityFilter(NonEmptyList(domain.Availability.PerDirect, List(domain.Availability.InOverleg))),
        DriversLicenseFilter(NonEmptyList(domain.DriversLicense("B"), List(domain.DriversLicense("C")))),
        LanguageFilter(NonEmptyList(domain.Language("English"), List(domain.Language("Dutch"))))
      )
    )
    expect.same(expectedFilters.map(_.toSet), parsedFilters.map(_.toSet)) // Compare sets to ignore order
  }

}
