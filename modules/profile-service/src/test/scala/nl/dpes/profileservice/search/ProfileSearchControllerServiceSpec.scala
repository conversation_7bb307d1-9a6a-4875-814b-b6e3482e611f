package nl.dpes.profileservice
package search

import cats.effect.IO
import cats.implicits.*
import io.scalaland.chimney.dsl.*
import nl.dpes.common.utils.Responses.{BadRequest, Forbidden, NotFound, Timeout, TooManyRequests}
import nl.dpes.profilesearch.creditservice
import nl.dpes.profilesearch.creditservice.CorrelationId
import nl.dpes.profilesearch.creditservice.CreditServiceClient.NotEntitled
import nl.dpes.profilesearch.domain.Error.ProfileNotFound
import nl.dpes.profilesearch.service.lock.Key
import nl.dpes.profilesearch.service.lock.LockingResult.LockTimedOut
import nl.dpes.profilesearch.service.model.detailpage.Profile
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profilesearch.service.model.profilesearch.suggestions.{CityPrefix, CitySuggestion}
import nl.dpes.profilesearch.service.model.profilesearch.{ProfileSearchResult, SearchResult, UpdatedDate}
import nl.dpes.profilesearch.service.visitedprofiles.VisitedProfilesService.ViewsExceeded
import nl.dpes.profilesearch.service.{visitedprofiles, ProfileSearchService}
import nl.dpes.profilesearch.service.model.detailpage
import nl.dpes.profilesearch.service.model.detailpage.*
import nl.dpes.profileservice.search.searchmodel.pagination.*
import nl.dpes.recruiterfavorites.service.FavoritesService
import nl.dpes.profileservice.search.RecruiterId as SearchRecruiterId
import nl.dpes.recruiterfavorites.service.domainmodel.{PaginatedProfileIds, ProfileId as FavoriteId, RecruiterId as FavoriteRecruiterId}
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.SimpleIOSuite

import scala.concurrent.duration.*

// todo: Use fixed date time in the test
object ProfileSearchControllerServiceSpec extends SimpleIOSuite {

  val profile: Profile = Profile(
    site = SiteName("nvb"),
    name = None,
    updatedDate = detailpage.UpdatedDate(123456789L),
    photo = None,
    introductionText = None,
    availability = None,
    requestedSalary = None,
    educationLevels = List(),
    preferredJobs = List(),
    functionGroups = List(),
    emailAddress = detailpage.EmailAddress("<EMAIL>"),
    phoneNumber = None,
    commute = None,
    workingHours = None,
    experiences = List(),
    educations = List(),
    training = List(),
    driversLicenses = List(),
    attachments = List(),
    isFavorite = false
  )

  def profileSearchResult(id: profilesearch.ProfileId): ProfileSearchResult =
    ProfileSearchResult(
      id,
      None,
      UpdatedDate(0),
      None,
      List(),
      List(),
      List(),
      None,
      None,
      None
    )

  def notImplemented[A](message: String): IO[A] = IO.raiseError[A](NotImplementedError(message))

  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  def pagination(number: Int, size: Int): IO[Pagination] =
    IO.fromEither((PageNumber(number), PageSize(size)).parMapN(Pagination.apply).leftMap(msg => new Exception(msg)))

  type GetProfileFn = (profilesearch.RecruiterId, profilesearch.ProfileId) => IO[Profile]
  type GetCitySuggestionsFn = CityPrefix => IO[List[CitySuggestion]]
  type GetProfilesFn = (
    profilesearch.pagination.Pagination,
    List[nl.dpes.profilesearch.domain.filter.Filter],
    Option[profilesearch.sort.SortField],
    Option[profilesearch.sort.SortOrder],
    Boolean
  ) => IO[SearchResult]
  type GetProfilesByIdFn = List[profilesearch.ProfileId] => IO[profilesearch.SearchResult]

  def mockFavoritesService(
    filterFavoritesFn: (FavoriteRecruiterId, List[FavoriteId]) => IO[List[FavoriteId]] = (_, _) => IO(List()),
    isFavoriteFn: => IO[Boolean] = IO(true)
  ): IO[FavoritesService[IO]] = IO {
    import nl.dpes.recruiterfavorites.service.domainmodel.*
    new FavoritesService[IO] {
      override def saveFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Unit] = notImplemented[Unit]("saveFavorite")
      override def deleteFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Unit] = notImplemented[Unit]("deleteFavorite")
      override def isFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Boolean] = isFavoriteFn
      override def filterFavorites(recruiterId: RecruiterId, profileIds: List[ProfileId]): IO[List[ProfileId]] =
        filterFavoritesFn(recruiterId, profileIds)
      override def getFavoritesForRecruiter(recruiterId: RecruiterId, page: Long = 1, limit: Long = 10): IO[PaginatedProfileIds] =
        notImplemented[PaginatedProfileIds]("getFavoritesForRecruiter")
    }
  }

  def mockProfileSearchService(
    getProfileFn: GetProfileFn = (_, _) => IO(profile),
    getCitySuggestionsFn: GetCitySuggestionsFn = _ => notImplemented[List[CitySuggestion]]("getCitySuggestionsFn"),
    getProfilesFn: GetProfilesFn = (_, _, _, _, _) => notImplemented[SearchResult]("getProfilesFn"),
    getProfilesByIdFn: GetProfilesByIdFn = _ => notImplemented[profilesearch.SearchResult]("getProfilesByIdFn")
  ): IO[ProfileSearchService[IO]] = IO {
    import nl.dpes.profilesearch.domain
    import nl.dpes.profilesearch.service.model.{detailpage, profilesearch}

    new ProfileSearchService[IO] {
      override def getCitySuggestions(
        cityPrefix: profilesearch.suggestions.CityPrefix
      ): IO[List[profilesearch.suggestions.CitySuggestion]] = getCitySuggestionsFn(cityPrefix)

      override def getProfiles(
        pagination: profilesearch.pagination.Pagination,
        filters: List[domain.filter.Filter],
        sortField: Option[profilesearch.sort.SortField],
        sortOrder: Option[profilesearch.sort.SortOrder],
        recruiterId: Option[profilesearch.RecruiterId],
        withoutAggregations: Boolean
      ): IO[SearchResult] = getProfilesFn(pagination, filters, sortField, sortOrder, withoutAggregations)

      override def getProfilesById(
        ids: List[profilesearch.ProfileId],
        recruiterId: Option[profilesearch.RecruiterId]
      ): IO[profilesearch.SearchResult] =
        getProfilesByIdFn(ids)

      override def getProfile(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): IO[detailpage.Profile] =
        getProfileFn(recruiterId, profileId)
    }
  }

  test("It should check that a profile was set as favorite") {

    for {
      profileId            <- IO.randomUUID.map(_.toString).map(ProfileId.unsafeApply)
      profileSearchService <- mockProfileSearchService()
      favoritesService     <- mockFavoritesService(isFavoriteFn = IO(true))
      service              <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result               <- service.getProfile(SearchRecruiterId.unsafeApply("123456789123456789"), profileId)
    } yield expect(result.map(_.isFavorite) == Right(true))
  }

  test("It should check that a profile was not set as favorite") {

    for {
      profileId            <- IO.randomUUID.map(_.toString).map(ProfileId.unsafeApply)
      profileSearchService <- mockProfileSearchService()
      favoritesService     <- mockFavoritesService(isFavoriteFn = IO(false))
      service              <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result               <- service.getProfile(SearchRecruiterId.unsafeApply("123456789123456789"), profileId)
    } yield expect(result.map(_.isFavorite) == Right(false))
  }

  test("It should return notfound when a specific profile is not found") {

    for {
      profileId            <- IO.randomUUID.map(_.toString).map(ProfileId.unsafeApply)
      profileSearchService <- mockProfileSearchService(getProfileFn = (recruiterId, profileId) => IO.raiseError(ProfileNotFound(profileId)))
      favoritesService     <- mockFavoritesService()
      service              <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result               <- service.getProfile(SearchRecruiterId.unsafeApply("123456789123456789"), profileId)
    } yield expect(result == Left(NotFound(s"Failed to load profile ${profileId.value}: Not Found")))
  }

  test("It should return too many requests when the view limit was reached") {
    for {
      profileId <- IO.randomUUID.map(_.toString).map(ProfileId.unsafeApply)
      profileSearchService <- mockProfileSearchService(getProfileFn =
        (recruiterId, profileId) =>
          IO.raiseError(
            ViewsExceeded(recruiterId.into[visitedprofiles.RecruiterId].transform, profileId.into[visitedprofiles.ProfileId].transform)
          )
      )
      favoritesService <- mockFavoritesService()
      service          <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result           <- service.getProfile(SearchRecruiterId.unsafeApply("123456789123456789"), profileId)
    } yield expect(result == Left(TooManyRequests("Profile view limit exceeded")))
  }

  test("It should return not allowed when a recruiter was not entitled to view a profile") {
    for {
      profileId     <- IO.randomUUID.map(_.toString).map(ProfileId.unsafeApply)
      correlationId <- CorrelationId.generate[IO]
      profileSearchService <- mockProfileSearchService(getProfileFn =
        (recruiterId, profileId) =>
          IO.raiseError(
            NotEntitled(recruiterId.into[creditservice.RecruiterId].transform, correlationId)
          )
      )
      favoritesService <- mockFavoritesService()
      service          <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result           <- service.getProfile(SearchRecruiterId.unsafeApply("123456789123456789"), profileId)
    } yield expect(result == Left(Forbidden("Not entitled to view profiles")))
  }

  test("when retrieving a profile times out a Timeout is returned") {
    for {
      profileId <- IO.randomUUID.map(_.toString).map(ProfileId.unsafeApply)
      profileSearchService <- mockProfileSearchService(getProfileFn =
        (recruiterId, profileId) => IO.raiseError(LockTimedOut(Key("1234"), 10.seconds))
      )
      favoritesService <- mockFavoritesService()
      service          <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result           <- service.getProfile(SearchRecruiterId.unsafeApply("123456789123456789"), profileId)
    } yield result match {
      case Left(Timeout(message)) => expect(clue(message) == s"Retieving profile ${profileId.value} timed out")
      case _                      => failure("Expected a Timeout error")
    }
  }

  test("City suggestions are forwarded on success") {
    val prefix = "first"
    for {
      profileSearchService <- mockProfileSearchService(getCitySuggestionsFn =
        prefix => IO(List(CitySuggestion(s"${prefix.value}amsterdam")))
      )
      favoritesService <- mockFavoritesService()
      service          <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result <- service.getCitySuggestions(CityPrefix(prefix).into[nl.dpes.profileservice.search.suggestions.CityPrefix].transform)
    } yield expect(result == Right(List(CitySuggestion("firstamsterdam"))))
  }

  test("City suggestions return an empty list on failure") {
    val prefix = "first"
    for {
      profileSearchService <- mockProfileSearchService(getCitySuggestionsFn =
        _ => IO.raiseError(new Exception("Failed to get suggestions"))
      )
      favoritesService <- mockFavoritesService()
      service          <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result <- service.getCitySuggestions(CityPrefix(prefix).into[nl.dpes.profileservice.search.suggestions.CityPrefix].transform)
    } yield expect(result == Left(BadRequest(s"Failed to get suggestions")))
  }

  test("It should return a list of profiles") {
    for {
      pagination <- pagination(1, 10)
      profileSearchService <- mockProfileSearchService(getProfilesFn =
        (pagination, filters, sortField, sortOrder, withoutAggregations) => IO.pure(SearchResult(List.empty, Map.empty, 0))
      )
      favoritesService <- mockFavoritesService()
      service          <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result           <- service.getProfiles(pagination, List.empty, None, None, withoutAggregations = false)
    } yield {
      val expected = searchmodel.SearchResult(List.empty, Map.empty, 0)
      expect(result == Right(expected))
    }
  }

  test("When no recruiter id has been provided the favorites are not checked") {
    for {
      pagination   <- pagination(1, 10)
      serviceCalls <- IO.ref(0)
      profileSearchService <- mockProfileSearchService(getProfilesFn =
        (pagination, filters, sortField, sortOrder, withoutAggregations) =>
          IO.pure(SearchResult(List(profileSearchResult(profilesearch.ProfileId.unsafe("1"))), Map.empty, 0))
      )
      favoritesService     <- mockFavoritesService(filterFavoritesFn = (_, _) => serviceCalls.update(_ + 1) *> IO.pure(List.empty))
      service              <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result               <- service.getProfiles(pagination, List.empty, None, None, withoutAggregations = false, None)
      amountOfServiceCalls <- serviceCalls.get
    } yield {
      val profileIsFavorite: Option[Boolean] = result.toOption.flatMap(_.profiles.headOption.map(_.isFavorite))
      expect(profileIsFavorite == Option(false))
      && expect(amountOfServiceCalls == 0)
    }
  }

  test("When a recruiter id has been provided and the profile id was not favorite, the profile will not be marked as favorite") {
    for {
      pagination   <- pagination(1, 10)
      serviceCalls <- IO.ref(0)
      profileSearchService <- mockProfileSearchService(getProfilesFn =
        (pagination, filters, sortField, sortOrder, withoutAggregations) =>
          IO.pure(SearchResult(List(profileSearchResult(profilesearch.ProfileId.unsafe("1"))), Map.empty, 0))
      )
      favoritesService <- mockFavoritesService(filterFavoritesFn = (_, _) => serviceCalls.update(_ + 1) *> IO.pure(List(FavoriteId("2"))))
      service          <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result <- service.getProfiles(
        pagination,
        List.empty,
        None,
        None,
        withoutAggregations = false,
        Some(SearchRecruiterId.unsafeApply("123456789123456789"))
      )
      amountOfServiceCalls <- serviceCalls.get
    } yield {
      val profileIsFavorite: Option[Boolean] = result.toOption.flatMap(_.profiles.headOption.map(_.isFavorite))
      expect(profileIsFavorite == Option(false))
      && expect(amountOfServiceCalls == 1)
    }
  }

  test("When a recruiter id has been provided and the profile id was a favorite, the profile will be marked as favorite") {
    for {
      pagination   <- pagination(1, 10)
      serviceCalls <- IO.ref(0)
      profileSearchService <- mockProfileSearchService(getProfilesFn =
        (pagination, filters, sortField, sortOrder, withoutAggregations) =>
          IO.pure(SearchResult(List(profileSearchResult(profilesearch.ProfileId.unsafe("1"))), Map.empty, 0))
      )
      favoritesService <- mockFavoritesService(filterFavoritesFn = (_, _) => serviceCalls.update(_ + 1) *> IO.pure(List(FavoriteId("1"))))
      service          <- IO(ProfileSearchControllerService.impl[IO](profileSearchService, favoritesService))
      result <- service.getProfiles(
        pagination,
        List.empty,
        None,
        None,
        withoutAggregations = false,
        Some(SearchRecruiterId.unsafeApply("123456789123456789"))
      )
      amountOfServiceCalls <- serviceCalls.get
    } yield {
      val profileIsFavorite: Option[Boolean] = result.toOption.flatMap(_.profiles.headOption.map(_.isFavorite))
      expect(profileIsFavorite == Option(true))
      && expect(amountOfServiceCalls == 1)
    }
  }
}
