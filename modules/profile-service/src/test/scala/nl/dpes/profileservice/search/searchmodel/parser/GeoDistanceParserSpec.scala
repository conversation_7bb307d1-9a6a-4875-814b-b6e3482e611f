package nl.dpes.profileservice.search.searchmodel.parser

import nl.dpes.profilesearch.domain.filter.GeoDistanceFilter
import weaver.FunSuite

object GeoDistanceParserSpec extends FunSuite {
  test("The geo distance parser should parse valid geo coordinates") {
    val input = "37.7749,2.4194"
    val expected = Right(GeoDistanceFilter(37.7749, 2.4194))

    val actual = GeoDistanceParser.parse(input)

    expect(actual == expected)
  }

  test("The parser should also tolerate spaces in the input") {
    val input = " 37.7749, 2.4194"
    val expected = Right(GeoDistanceFilter(37.7749, 2.4194))

    val actual = GeoDistanceParser.parse(input)

    expect(actual == expected)
  }

  test("The parser should return an error for invalid latitude") {
    val input = "invalid,12.23"
    val expected = Left(s"Invalid format for coordinates: invalid. It's expected to be a Double.")

    val actual = GeoDistanceParser.parse(input)

    expect(actual.left.map(_.getMessage) == expected, actual.toString)
  }

  test("The parser should return an error for invalid longitude") {
    val input = "12.23,invalid"
    val expected = Left(s"Invalid format for coordinates: invalid. It's expected to be a Double.")

    val actual = GeoDistanceParser.parse(input)

    expect(actual.left.map(_.getMessage) == expected, actual.toString)
  }
}
