package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.UpdatedDate
import weaver.FunSuite

object UpdatedDateSpec extends FunSuite {

  test("UpdatedDate should be json encoded as a long") {
    val updatedDate = UpdatedDate(*********)
    val json = updatedDate.asJson

    expect(json == *********.asJson)
  }

  test("UpdatedDate should be encoded and decoded correctly") {
    val updatedDate = UpdatedDate(*********)

    val roundTrippedUpdatedDate = decode[UpdatedDate](updatedDate.asJson.noSpaces)

    expect(roundTrippedUpdatedDate == Right(updatedDate))
  }
}
