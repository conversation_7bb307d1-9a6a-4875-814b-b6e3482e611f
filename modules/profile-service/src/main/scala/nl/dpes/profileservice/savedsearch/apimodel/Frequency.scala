package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import sttp.tapir.Schema

case class Frequency private (value: String)

object Frequency {

  val supportedValues: List[String] = List("<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Nooit")

  given Encoder[Frequency] = Encoder.encodeString.contramap(_.value)
  given Decoder[Frequency] = Decoder.decodeString.emap(Frequency(_))
  given Schema[Frequency] = Schema.string
  given Transformer[Frequency, nl.dpes.savedsearch.domain.Frequency] = frequency =>
    frequency.value match {
      case "Dagelijks" => nl.dpes.savedsearch.domain.Frequency.Daily
      case "Wekelijks" => nl.dpes.savedsearch.domain.Frequency.Weekly
      case "Nooit"     => nl.dpes.savedsearch.domain.Frequency.Never
    }

  def apply(frequency: String): Either[String, Frequency] =
    if (supportedValues.contains(frequency)) Right(new Frequency(frequency))
    else Left(s"The provided frequency '$frequency' is not supported. The possible values are '$supportedValues'")

  def unsafeApply(frequency: String) = new Frequency(frequency)
}
