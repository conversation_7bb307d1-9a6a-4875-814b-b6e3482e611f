package nl.dpes.profileservice.tracing

import cats.effect.*
import cats.implicits.*
import io.circe.Encoder
import nl.dpes.common.utils.Responses.*
import org.typelevel.log4cats.SelfAwareStructuredLogger
import sttp.model.StatusCode
import sttp.tapir.*
import sttp.tapir.DecodeResult.Error.{JsonDecodeException, JsonError}
import sttp.tapir.generic.auto.*
import sttp.tapir.json.circe.jsonBody
import sttp.tapir.server.http4s.Http4sServerOptions
import sttp.tapir.server.interceptor.decodefailure.DecodeFailureHandler
import sttp.tapir.server.model.{ServerResponse, ValuedEndpointOutput}
import io.circe.syntax.*
import io.circe.generic.auto.*
import io.circe.Encoder.AsObject.importedAsObjectEncoder
import sttp.tapir.server.interceptor.log.DefaultServerLog
import sttp.tapir.model.ServerRequest

object HttpServerOptions {

  def serverOptions[F[_]: Async](logger: SelfAwareStructuredLogger[F]): Http4sServerOptions[F] = {

    val serverLog = DefaultServerLog[F](
      doLogWhenReceived = req => logger.info(req),
      doLogWhenHandled = (req, thr) =>
        thr match {
          case Some(_: Throwable) if req.contains("\"statusCode\":\"400\"") => logger.warn(req)
          case Some(_: Throwable)                                           => logger.error(req)
          case None                                                         => logger.info(req)
        },
      doLogAllDecodeFailures = (msg, _) => logger.error(msg),
      doLogExceptions = (msg, thr) => logger.error(s"$msg; Exception: ${thr.getMessage}, Stacktrace: ${thr.getStackTrace.mkString("\n")}"),
      noLog = Async[F].unit,
      showRequest = requestToJson,
      showResponse = responseToJson
    )

    Http4sServerOptions.customiseInterceptors
      .serverLog(serverLog)
      .decodeFailureHandler(DecodeFailureHandler[F] { ctx =>
        ctx.failure match {
          case DecodeResult.Error(_, error) =>
            error match {
              case JsonDecodeException(errors: List[JsonError], _: Throwable) =>
                ValuedEndpointOutput(
                  jsonBody[BadRequest].and(statusCode(StatusCode.BadRequest)),
                  BadRequest(errors.map(_.msg).mkString("."))
                ).some.pure
              case e =>
                ValuedEndpointOutput(
                  jsonBody[BadRequest].and(statusCode(StatusCode.BadRequest)),
                  BadRequest(e.getMessage)
                ).some.pure
            }
          case _ => None.pure
        }
      })
      .options
  }

  private def requestToJson(req: ServerRequest): String =
    Map(
      "method"      -> req.method.method,
      "path"        -> req.pathSegments.mkString("/"),
      "recruiterId" -> req.headers.find(_.name == "X-Recruiter-ID").toString
    ).asJson.noSpaces

  private def responseToJson(resp: ServerResponse[?]): String =
    Map(
      "statusCode" -> resp.code.code.toString,
      "message"    -> resp.source.map(_.value).toString
    ).asJson.noSpaces
}
