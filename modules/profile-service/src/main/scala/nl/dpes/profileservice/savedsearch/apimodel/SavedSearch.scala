package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.generic.semiauto.*
import sttp.tapir.Schema

case class SavedSearch(
  id: SavedSearchId,
  recruiterId: RecruiterId,
  name: SavedSearchName,
  frequency: Frequency,
  searchFilters: SearchFilters
)

object SavedSearch {
  given Encoder[SavedSearch] = deriveEncoder
  given Decoder[SavedSearch] = deriveDecoder
  given Schema[SavedSearch] = Schema.derived[SavedSearch]
}
