package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import sttp.tapir.{Codec, Schema}
import sttp.tapir.CodecFormat.TextPlain

opaque type SavedSearchId = String

object SavedSearchId {
  given Encoder[SavedSearchId] = Encoder.encodeString
  given Decoder[SavedSearchId] = Decoder.decodeString
  given Codec[String, SavedSearchId, TextPlain] = Codec.string.map(SavedSearchId(_))(_.value)
  given Schema[SavedSearchId] = Schema.string

  def apply(savedSearchId: String): SavedSearchId = savedSearchId

  extension (savedSearchId: SavedSearchId) def value: String = savedSearchId
}
