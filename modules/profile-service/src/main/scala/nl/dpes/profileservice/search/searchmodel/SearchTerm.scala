package nl.dpes.profileservice.search.searchmodel

import nl.dpes.profilesearch.utils.OptionalString
import sttp.tapir.{Codec, CodecFormat}

case class SearchTerm(value: String) extends AnyVal
object SearchTerm {
  given Codec[String, SearchTerm, CodecFormat.TextPlain] = Codec.string.map(SearchTerm(_))(_.value)

  def cleanupSearchTerm: Option[SearchTerm] => Option[SearchTerm] =
    OptionalString.cleanup(_)(_.value, SearchTerm.apply)
}
