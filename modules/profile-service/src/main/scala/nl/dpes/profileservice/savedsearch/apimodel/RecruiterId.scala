package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import sttp.tapir.CodecFormat.TextPlain
import sttp.tapir.{Codec, Schema}
import nl.dpes.savedsearch

case class RecruiterId private (value: String)

object RecruiterId {
  given Encoder[RecruiterId] = Encoder.encodeString.contramap(_.value)
  given Decoder[RecruiterId] = Decoder.decodeString.emap(RecruiterId(_))
  given Codec[String, RecruiterId, TextPlain] = Codec.string.mapEither(RecruiterId(_))(_.value)
  given Schema[RecruiterId] = Schema.string
  given Transformer[RecruiterId, savedsearch.domain.RecruiterId] = recruiterId => savedsearch.domain.RecruiterId(recruiterId.value)

  def apply(recruiterId: String): Either[String, RecruiterId] =
    if (recruiterId.trim.length == 18)
      Right(new RecruiterId(recruiterId.trim))
    else
      Left(s"RecruiterId is a 18-character identifier. You have provided '${recruiterId.trim.length}' characters.")

  def unsafeApply(value: String): RecruiterId = new RecruiterId(value)
}
