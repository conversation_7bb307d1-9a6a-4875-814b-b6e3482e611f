package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.generic.semiauto.*
import sttp.tapir.Schema.annotations.description

case class SavedSearchCreation(
  name: SavedSearchName,
  @description("Represents the search frequency. When creating a saved search, it supports these values: '<PERSON><PERSON><PERSON><PERSON><PERSON>' and '<PERSON><PERSON>i<PERSON><PERSON>'.")
  frequency: Frequency,
  searchFilters: SearchFilters
)

object SavedSearchCreation {
  given Encoder[SavedSearchCreation] = deriveEncoder
  given Decoder[SavedSearchCreation] = deriveDecoder

  object SwaggerDoc {
    val example: SavedSearchCreation = SavedSearchCreation(
      name = SavedSearchName("Scala developer"),
      frequency = Frequency.unsafeApply("Dagelijks"),
      searchFilters = SearchFilters(
        searchTerm = Some(SearchTerm("Scala developer")),
        city = Some(City("Amsterdam")),
        geoDistance = Some(GeoDistance(Latitude("52.377956"), Longitude("4.897070"))),
        provinces = Some(Seq(Province("Noord-Holland"))),
        updatedDate = Some(UpdatedDate("Alles")),
        functionGroups = Some(Seq(FunctionGroup("AdministratiefSecretarieel"), FunctionGroup("FinancieelAccounting"))),
        workLevels = Some(Seq(WorkLevel("HAVO"), WorkLevel("Postdoctoraal"), WorkLevel("Lagere school"))),
        workingHours = Some(Seq(WorkingHour("16 tot 24 uur"), WorkingHour("24 tot 32 uur"))),
        careerLevels = Some(Seq(CareerLevel("Starter"), CareerLevel("Ervaren"), CareerLevel("Leidinggevend"))),
        requestedSalaries = Some(Seq(RequestedSalary("2.500 - 3.500"), RequestedSalary("3.500 - 5.000"))),
        availabilities = Some(Seq(Availability("Per direct"), Availability("In overleg"))),
        driversLicenses = Some(Seq(DriverLicense("B - personenauto"))),
        languages = Some(Seq(Language("Arabisch"), Language("Engles"), Language("Nederlands")))
      )
    )
  }
}
