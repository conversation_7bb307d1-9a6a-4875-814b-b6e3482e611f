package nl.dpes.profileservice.savedsearch

import cats.effect.Async
import cats.implicits.*
import io.circe.generic.auto.*
import nl.dpes.profileservice.savedsearch.apimodel.*
import nl.dpes.common.utils.Responses.BadRequest
import sttp.model.StatusCode
import sttp.tapir.*
import sttp.tapir.generic.auto.*
import sttp.tapir.json.circe.jsonBody
import sttp.tapir.server.ServerEndpoint

trait SavedSearchController[F[_]] {
  def getById: F[ServerEndpoint[Any, F]]
  def create: F[ServerEndpoint[Any, F]]
  def update: F[ServerEndpoint[Any, F]]
  def delete: F[ServerEndpoint[Any, F]]
  def getAll: F[ServerEndpoint[Any, F]]
  def endpoints: F[List[ServerEndpoint[Any, F]]]
}

object SavedSearchController {

  def impl[F[_]: Async](savedSearchService: SavedSearchControllerService[F]): SavedSearchController[F] = new SavedSearchController[F] {
    override def getById: F[ServerEndpoint[Any, F]] =
      Async[F].delay {
        endpoint.get
          .description("Returns a saved search by its id")
          .in(header[RecruiterId]("X-Recruiter-ID").description("The id of the recruiter"))
          .in("savedsearches" / path[SavedSearchId]("savedSearchId").description("The id of the saved search"))
          .out(jsonBody[SavedSearch])
          .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
          .serverLogic(savedSearchService.getById)
      }

    override def create: F[ServerEndpoint[Any, F]] =
      Async[F].delay {
        endpoint.post
          .description("Saves a saved search")
          .in("savedsearches")
          .in(header[RecruiterId]("X-Recruiter-ID").description("The id of the recruiter"))
          .in(jsonBody[SavedSearchCreation].example(SavedSearchCreation.SwaggerDoc.example))
          .out(statusCode(StatusCode.Created))
          .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
          .serverLogic(savedSearchService.create)
      }

    override def update: F[ServerEndpoint[Any, F]] =
      Async[F].delay {
        endpoint.patch
          .description("Updates the frequency of a saved search")
          .in(header[RecruiterId]("X-Recruiter-ID").description("The id of the recruiter"))
          .in("savedsearches" / path[SavedSearchId]("savedSearchId").description("The id of the saved search"))
          .in(jsonBody[SavedSearchUpdate].example(SavedSearchUpdate.SwaggerDoc.example))
          .out(statusCode(StatusCode.Ok))
          .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
          .serverLogic(savedSearchService.update)
      }

    override def delete: F[ServerEndpoint[Any, F]] =
      Async[F].delay {
        endpoint.delete
          .description("Deletes a saved search")
          .in(header[RecruiterId]("X-Recruiter-ID").description("The id of the recruiter"))
          .in("savedsearches" / path[SavedSearchId]("savedSearchId").description("The id of the saved search"))
          .out(statusCode(StatusCode.Ok))
          .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
          .serverLogic(savedSearchService.delete)
      }

    override def getAll: F[ServerEndpoint[Any, F]] =
      Async[F].delay {
        endpoint.get
          .description("Returns all the saved searches for a recruiter")
          .in(header[RecruiterId]("X-Recruiter-ID").description("The id of the recruiter"))
          .in("savedsearches")
          .out(jsonBody[List[SavedSearch]])
          .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
          .serverLogic(savedSearchService.getAll)
      }

    override def endpoints: F[List[ServerEndpoint[Any, F]]] = List(getById, create, update, delete, getAll).sequence
  }
}
