package nl.dpes.profileservice.savedsearch.apimodel

import cats.implicits.toTraverseOps
import cats.data.NonEmptyList
import io.circe.*
import io.circe.generic.semiauto.*
import io.scalaland.chimney.*
import io.scalaland.chimney.dsl.*
import sttp.tapir.Schema
import nl.dpes.profilesearch.domain
import nl.dpes.profilesearch.domain.filter.*

case class SearchFilters(
  searchTerm: Option[SearchTerm],
  city: Option[City],
  geoDistance: Option[GeoDistance],
  provinces: Option[Seq[Province]],
  updatedDate: Option[UpdatedDate],
  functionGroups: Option[Seq[FunctionGroup]],
  workLevels: Option[Seq[WorkLevel]],
  workingHours: Option[Seq[WorkingHour]],
  careerLevels: Option[Seq[CareerLevel]],
  requestedSalaries: Option[Seq[RequestedSalary]],
  availabilities: Option[Seq[Availability]],
  driversLicenses: Option[Seq[DriverLicense]],
  languages: Option[Seq[Language]]
)

object SearchFilters {
  given Encoder[SearchFilters] = deriveEncoder
  given Decoder[SearchFilters] = deriveDecoder
  given Schema[SearchFilters] = Schema.derived[SearchFilters]
  given PartialTransformer[SearchFilters, nl.dpes.savedsearch.domain.SavedSearchFilters] = PartialTransformer { searchFilters =>
    partial.Result.fromEitherString(
      for {
        searchTerm <- Right(
          searchFilters.searchTerm.map(_.toString).flatMap(str => domain.SearchTerm.fromString(str).map(SearchTermFilter(_)))
        )
        latitude <- searchFilters.geoDistance
          .flatMap(_.latitude.toString.toDoubleOption)
          .toRight("Invalid latitude in geoDistance")
        longitude <- searchFilters.geoDistance
          .flatMap(_.longitude.toString.toDoubleOption)
          .toRight("Invalid longitude in geoDistance")
        updatedDate <- searchFilters.updatedDate
          .map(updatedDate => domain.UpdateDate.fromString(updatedDate.toString).toRight("Invalid updatedDate"))
          .sequence
      } yield nl.dpes.savedsearch.domain.SavedSearchFilters(
        searchTerm = searchTerm,
        city = searchFilters.city.map(_.toString).map(str => domain.filter.CityFilter(domain.City(str))),
        geoDistance = searchFilters.geoDistance.map(geoDistance => domain.filter.GeoDistanceFilter(latitude, longitude)),
        provinces = NonEmptyList
          .fromFoldable(searchFilters.provinces.getOrElse(Seq.empty))
          .map(provinces => domain.filter.ProvinceFilter(provinces.map(province => domain.Province(province.toString))))
          .toSeq,
        updatedDate = updatedDate.map(UpdateDateFilter(_)),
        functionGroups = NonEmptyList
          .fromFoldable(
            searchFilters.functionGroups.getOrElse(Seq.empty).map(functionGroup => domain.FunctionGroup(functionGroup.toString))
          )
          .map(FunctionGroupFilter(_)),
        workLevels = NonEmptyList
          .fromFoldable(
            searchFilters.workLevels.getOrElse(Seq.empty).map(workLevel => domain.WorkLevel(workLevel.toString))
          )
          .map(WorkLevelFilter(_)),
        workingHours = None,
        careerLevels = NonEmptyList
          .fromFoldable(
            searchFilters.careerLevels.getOrElse(Seq.empty).map(careerLevel => domain.CareerLevel(careerLevel.toString))
          )
          .map(CareerLevelFilter(_)),
        requestedSalaries = NonEmptyList
          .fromFoldable(
            searchFilters.requestedSalaries
              .getOrElse(Seq.empty)
              .flatMap(requestedSalary => domain.RequestedSalary.fromString(requestedSalary.toString))
          )
          .map(RequestedSalaryFilter(_)),
        availabilities = NonEmptyList
          .fromFoldable(
            searchFilters.availabilities.getOrElse(Seq.empty).flatMap(availability => domain.Availability.fromString(availability.toString))
          )
          .map(AvailabilityFilter(_)),
        driversLicenses = NonEmptyList
          .fromFoldable(
            searchFilters.driversLicenses.getOrElse(Seq.empty).map(driverLicense => domain.DriversLicense(driverLicense.toString))
          )
          .map(DriversLicenseFilter(_)),
        languages = NonEmptyList
          .fromFoldable(
            searchFilters.languages.getOrElse(Seq.empty).map(language => domain.Language(language.toString))
          )
          .map(LanguageFilter(_))
      )
    )
  }

}
