package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.generic.semiauto.*
import sttp.tapir.Schema

case class SearchFilters(
  searchTerm: Option[SearchTerm],
  city: Option[City],
  geoDistance: Option[GeoDistance],
  provinces: Option[Seq[Province]],
  updatedDate: Option[UpdatedDate],
  functionGroups: Option[Seq[FunctionGroup]],
  workLevels: Option[Seq[WorkLevel]],
  workingHours: Option[Seq[WorkingHour]],
  careerLevels: Option[Seq[CareerLevel]],
  requestedSalaries: Option[Seq[RequestedSalary]],
  availabilities: Option[Seq[Availability]],
  driversLicenses: Option[Seq[DriverLicense]],
  languages: Option[Seq[Language]]
)

object SearchFilters {
  given Encoder[SearchFilters] = deriveEncoder
  given Decoder[SearchFilters] = deriveDecoder
  given Schema[SearchFilters] = Schema.derived[SearchFilters]
}
