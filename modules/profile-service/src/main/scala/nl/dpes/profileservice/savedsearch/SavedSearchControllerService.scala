package nl.dpes.profileservice.savedsearch

import cats.effect.{Async, Resource}
import nl.dpes.common.utils.Responses.BadRequest
import nl.dpes.profileservice.savedsearch.apimodel.*
import nl.dpes.savedsearch

trait SavedSearchControllerService[F[_]] {

  def getById(recruiterId: RecruiterId, id: SavedSearchId): F[Either[BadRequest, SavedSearch]]

  def create(recruiterId: RecruiterId, searchFilters: SavedSearchCreation): F[Either[BadRequest, Unit]]

  def update(recruiterId: RecruiterId, id: SavedSearchId, update: SavedSearchUpdate): F[Either[BadRequest, Unit]]

  def delete(recruiterId: RecruiterId, id: SavedSearchId): F[Either[BadRequest, Unit]]

  def getAll(recruiterId: RecruiterId): F[Either[BadRequest, List[SavedSearch]]]
}

object SavedSearchControllerService {

  def impl[F[_]: Async](savedSearchService: savedsearch.service.SavedSearchService[F]): SavedSearchControllerService[F] =
    new SavedSearchControllerService[F] {
      override def getById(recruiterId: RecruiterId, id: SavedSearchId): F[Either[BadRequest, SavedSearch]] = ???

      override def create(recruiterId: RecruiterId, searchFilters: SavedSearchCreation): F[Either[BadRequest, Unit]] = ???

      override def update(recruiterId: RecruiterId, id: SavedSearchId, update: SavedSearchUpdate): F[Either[BadRequest, Unit]] = ???

      override def delete(recruiterId: RecruiterId, id: SavedSearchId): F[Either[BadRequest, Unit]] = ???

      override def getAll(recruiterId: RecruiterId): F[Either[BadRequest, List[SavedSearch]]] = ???
    }

  def resource[F[_]: Async](savedSearchService: savedsearch.service.SavedSearchService[F]): Resource[F, SavedSearchControllerService[F]] =
    Resource.pure(SavedSearchControllerService.impl(savedSearchService))
}
