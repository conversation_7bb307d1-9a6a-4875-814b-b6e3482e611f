package nl.dpes.profileservice.savedsearch

import cats.data.EitherT
import cats.effect.{Async, Resource}
import cats.implicits.*
import io.scalaland.chimney.dsl.*
import nl.dpes.common.utils.Responses.BadRequest
import nl.dpes.profileservice.savedsearch.apimodel.*
import nl.dpes.savedsearch

trait SavedSearchControllerService[F[_]] {

  def getById(recruiterId: RecruiterId, id: SavedSearchId): F[Either[BadRequest, SavedSearch]]

  def create(recruiterId: RecruiterId, searchFilters: SavedSearchCreation): F[Either[BadRequest, Unit]]

  def update(recruiterId: RecruiterId, id: SavedSearchId, update: SavedSearchUpdate): F[Either[BadRequest, Unit]]

  def delete(recruiterId: RecruiterId, id: SavedSearchId): F[Either[BadRequest, Unit]]

  def getAll(recruiterId: RecruiterId): F[Either[BadRequest, List[SavedSearch]]]
}

object SavedSearchControllerService {

  def impl[F[_]: Async](savedSearchService: savedsearch.service.SavedSearchService[F]): SavedSearchControllerService[F] =
    new SavedSearchControllerService[F] {
      override def getById(recruiterId: RecruiterId, id: SavedSearchId): F[Either[BadRequest, SavedSearch]] = ???

      override def create(recruiterId: RecruiterId, searchFilters: SavedSearchCreation): F[Either[BadRequest, Unit]] =
        (for {
          savedSearchFilters <- EitherT(
            Async[F].pure(searchFilters.searchFilters.intoPartial[savedsearch.domain.SavedSearchFilters].transform.asEither.leftMap { e =>
              BadRequest(s"Invalid search filters: ${e.errors.mkString(", ")}")
            })
          )
          result <- EitherT[F, BadRequest, Unit](
            savedSearchService
              .create(
                recruiterId.into[savedsearch.domain.RecruiterId].transform,
                searchFilters.name.toString,
                searchFilters.frequency.into[savedsearch.domain.Frequency].transform,
                savedSearchFilters
              )
              .map(_ => Right(()))
          )
        } yield result).value
          .handleError(_ => Left(BadRequest("Failed to create saved search")))

      override def update(recruiterId: RecruiterId, id: SavedSearchId, update: SavedSearchUpdate): F[Either[BadRequest, Unit]] = ???

      override def delete(recruiterId: RecruiterId, id: SavedSearchId): F[Either[BadRequest, Unit]] = ???

      override def getAll(recruiterId: RecruiterId): F[Either[BadRequest, List[SavedSearch]]] = ???
    }

  def resource[F[_]: Async](savedSearchService: savedsearch.service.SavedSearchService[F]): Resource[F, SavedSearchControllerService[F]] =
    Resource.pure(SavedSearchControllerService.impl(savedSearchService))
}
