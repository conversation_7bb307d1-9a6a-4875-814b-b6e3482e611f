package nl.dpes.profileservice

import cats.effect.*
import com.sksamuel.elastic4s.cats.effect.instances.*
import com.sksamuel.elastic4s.http.JavaClient
import com.sksamuel.elastic4s.{ElasticClient, ElasticNodeEndpoint, ElasticProperties}
import doobie.Transactor
import nl.dpes.profilesearch.config.Syntax.*
import nl.dpes.profilesearch.config.ElasticsearchConfig
import nl.dpes.profilesearch.creditservice.CreditService
import nl.dpes.profilesearch.database.Database
import nl.dpes.profilesearch.service.ProfileSearchService
import nl.dpes.profilesearch.service.lock.{LockManager, MysqlLockService}
import nl.dpes.profilesearch.service.unlockedprofiles.{UnlockedProfileConfig, UnlockedProfileRepository, UnlockedProfileService}
import nl.dpes.profilesearch.service.viewedprofiles.{ViewedProfilesConfig, ViewedProfilesRepository, ViewedProfilesService}
import nl.dpes.profilesearch.service.visitedprofiles.*
import nl.dpes.profileservice.config.AppConfig
import nl.dpes.profileservice.status.StatusController
import nl.dpes.recruiterfavorites.recruiterfavoritesstorage.MysqlFavoritesRepository
import nl.dpes.recruiterfavorites.service.FavoritesService
import org.http4s.HttpRoutes
import org.http4s.blaze.server.BlazeServerBuilder
import org.http4s.server.Router
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import pureconfig.ConfigSource
import nl.dpes.profileservice.favorites.{FavoritesController, FavoritesControllerService as HttpFavoritesService}
import nl.dpes.profileservice.savedsearch.{SavedSearchController, SavedSearchControllerService}
import nl.dpes.savedsearch
import nl.dpes.profileservice.search.{ProfileSearchController, ProfileSearchControllerService}
import sttp.tapir.server.http4s.Http4sServerInterpreter
import sttp.tapir.swagger.SwaggerUIOptions
import sttp.tapir.swagger.bundle.SwaggerInterpreter
import nl.dpes.profileservice.tracing.HttpServerOptions
import nl.dpes.recruiterfavorites.recruiterfavoritesstorage.config.FavoriteProfilesConfig
import nl.dpes.savedsearch.config.SavedSearchConfig
import nl.dpes.savedsearch.service.SavedSearchRepository
import nl.dpes.savedsearch.storage.MySqlSavedSearchRepository
import org.http4s.Response

object Main extends IOApp {

  val version = "v1"

  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  override def run(args: List[String]): IO[ExitCode] =
    startHttpServer[IO]
      .use(_ => IO.never)

  private def visitedProfilesService[F[_]: {Async, LoggerFactory}](
    visitedProfilesConfig: VisitedProfilesConfig,
    xa: Transactor[F],
    creditService: CreditService[F],
    unlockedProfileService: UnlockedProfileService[F]
  ): Resource[F, VisitedProfilesService[F]] =
    for {
      repository <- VisitedProfilesRepository.resource[F](visitedProfilesConfig.tableName, xa)
      service    <- VisitedProfilesService.resource[F](visitedProfilesConfig.viewLimit, repository, creditService, unlockedProfileService)
    } yield service

  private def unlockedProfileService[F[_]: {Async, LoggerFactory}](unlockedProfileConfig: UnlockedProfileConfig, xa: Transactor[F]) =
    for {
      repository <- UnlockedProfileRepository.initResource(unlockedProfileConfig.tableName, xa)
      service    <- UnlockedProfileService.resource[F](repository, unlockedProfileConfig.accessDurationInDays)
    } yield service

  private def viewedProfilesService[F[_]: {Async, LoggerFactory}](viewedProfilesConfig: ViewedProfilesConfig, xa: Transactor[F]) =
    for {
      repository <- ViewedProfilesRepository.resource[F](viewedProfilesConfig.tableName, viewedProfilesConfig.retentionThresholdInDays, xa)
      service    <- Resource.pure(ViewedProfilesService.impl(repository))
    } yield service

  private def favoritesService[F[_]: {Async, LoggerFactory}](favoriteProfilesConfig: FavoriteProfilesConfig, xa: Transactor[F]) =
    for {
      repository <- MysqlFavoritesRepository.resource(favoriteProfilesConfig.tableName, xa)
      service    <- FavoritesService.resource[F](repository)
    } yield service

  private def savedSearchService[F[_]: {Async, LoggerFactory}](savedSearchConfig: SavedSearchConfig, xa: Transactor[F]) =
    for {
      repository <- savedSearchRepository(savedSearchConfig, xa)
      service    <- savedsearch.service.SavedSearchService.resource(repository)
    } yield service

  private def savedSearchRepository[F[_]: Async](savedSearchConfig: SavedSearchConfig, xa: Transactor[F]) =
    MySqlSavedSearchRepository.resource(savedSearchConfig.tableName, xa)

  private def startHttpServer[F[+_]: {Async, LoggerFactory}] =
    for {
      appConfig              <- Resource.eval(ConfigSource.default.loadF[F, AppConfig])
      elasticClient          <- createElasticClient(appConfig.elasticsearchConfig)
      dbTransactor           <- Database.resource[F](appConfig.databaseConfig)
      lockManager            <- Resource.eval(LockManager[F](new MysqlLockService(), dbTransactor, appConfig.lockConfig))
      creditService          <- CreditService.resource[F](appConfig.creditServiceConfig)
      unlockedProfileService <- unlockedProfileService(appConfig.unlockedProfileConfig, dbTransactor)
      viewedProfilesService  <- viewedProfilesService(appConfig.viewedProfilesConfig, dbTransactor)
      visitedProfilesService <- visitedProfilesService(appConfig.visitedProfilesConfig, dbTransactor, creditService, unlockedProfileService)
      profileSearchService <- ProfileSearchService.resource(
        appConfig.elasticsearchConfig.index,
        elasticClient,
        visitedProfilesService,
        viewedProfilesService,
        lockManager
      )
      savedSearchService             <- savedSearchService(appConfig.savedSearchConfig, dbTransactor)
      favoritesService               <- favoritesService(appConfig.favoriteProfilesConfig, dbTransactor)
      _                              <- savedSearchRepository(appConfig.savedSearchConfig, dbTransactor)
      profileSearchControllerService <- ProfileSearchControllerService.resource(profileSearchService, favoritesService)
      httpFavoritesService           <- HttpFavoritesService.resource(favoritesService, profileSearchService)
      savedSearchControllerService   <- SavedSearchControllerService.resource(savedSearchService)
      routes                         <- createRoutes[F](profileSearchControllerService, httpFavoritesService, savedSearchControllerService)
      server <- BlazeServerBuilder[F]
        .bindHttp(appConfig.blazeServerConfig.port, appConfig.blazeServerConfig.host)
        .withHttpApp(routes.orNotFound)
        .resource
    } yield server

  private def createRoutes[F[_]: Async](
    profileSearchService: ProfileSearchControllerService[F],
    favoritesService: HttpFavoritesService[F],
    savedSearchService: SavedSearchControllerService[F]
  ): Resource[F, HttpRoutes[F]] =
    Resource.eval(
      for {
        logger               <- Slf4jFactory.create[F].fromClass(getClass)
        statusRoute          <- StatusController.impl[F].routes
        profileEndpoints     <- ProfileSearchController.impl[F](profileSearchService).endpoints
        favoritesEndpoints   <- FavoritesController.impl[F](favoritesService).endpoints
        savedSearchEndpoints <- SavedSearchController.impl[F](savedSearchService).endpoints
        allEndpoints         <- ((profileEndpoints ++ favoritesEndpoints) ++ savedSearchEndpoints).pure
        interpreter          <- Http4sServerInterpreter[F](HttpServerOptions.serverOptions(logger)).pure
        swaggerRoutes <- Async[F].delay(
          interpreter
            .toRoutes(
              SwaggerInterpreter(swaggerUIOptions =
                SwaggerUIOptions(
                  List("docs", "profileservice", version),
                  "docs.yaml",
                  Nil,
                  useRelativePaths = false,
                  showExtensions = true,
                  None,
                  None
                )
              )
                .fromServerEndpoints[F](allEndpoints, "profileservice", version)
            )
        )
        routes <- interpreter.toRoutes(allEndpoints).pure
      } yield Router("/" -> (swaggerRoutes <+> statusRoute <+> routes))
    )

  private def createElasticClient[F[_]: Async](elasticsearchConfig: ElasticsearchConfig) =
    Resource.fromAutoCloseable[F, ElasticClient] {
      val elasticNodeEndpoint: ElasticNodeEndpoint =
        ElasticNodeEndpoint(elasticsearchConfig.hostScheme, elasticsearchConfig.host, elasticsearchConfig.port, None)
      Async[F].delay(ElasticClient(JavaClient(ElasticProperties(Seq(elasticNodeEndpoint)))))
    }
}
